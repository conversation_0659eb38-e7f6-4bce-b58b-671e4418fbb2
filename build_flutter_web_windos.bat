@echo off
setlocal enabledelayedexpansion

REM ###############################################################################
REM 🛠 FLUTTER WEB BUILD SCRIPT (Windows Batch Version)
REM
REM 📌 Description:
REM    - Increments version in `pubspec.yaml`
REM    - Builds Flutter web app
REM    - Modifies `index.html` and JS files to bust cache using version query
REM    - Sets custom base href
REM
REM ✅ Compatible With:
REM    - Windows CMD
REM    - Windows PowerShell
REM
REM 🚀 How to Run:
REM   - Double-click the .bat file, or
REM   - Open CMD/PowerShell in project folder and run:
REM     > build_flutter_web.bat
REM
REM 🧰 Prerequisites:
REM   - Flutter SDK
REM   - PowerShell (built into Windows)
REM
REM ###############################################################################

echo 🔄 Incrementing build version...

REM Extract current version using PowerShell
for /f "tokens=*" %%i in ('powershell -command "(Get-Content pubspec.yaml | Select-String '^version:').ToString().Split(' ')[1]"') do set FULL_VERSION=%%i

REM Split version and build number
for /f "tokens=1,2 delims=+" %%a in ("%FULL_VERSION%") do (
    set VERSION_BASE=%%a
    set BUILD_NUM=%%b
)

REM Extract major.minor and patch
for /f "tokens=1,2,3 delims=." %%a in ("%VERSION_BASE%") do (
    set MAJOR=%%a
    set MINOR=%%b
    set PATCH=%%c
)

REM Increment patch and build numbers
set /a NEW_PATCH=%PATCH%+1
set /a NEW_BUILD=%BUILD_NUM%+1

REM Construct new version
set NEW_VERSION=%MAJOR%.%MINOR%.%NEW_PATCH%+%NEW_BUILD%
set VERSION_NO_PLUS=%MAJOR%.%MINOR%.%NEW_PATCH%%NEW_BUILD%

echo 📦 Updating pubspec.yaml to version: %NEW_VERSION%

REM Update pubspec.yaml using PowerShell
powershell -command "(Get-Content pubspec.yaml) -replace '^version:.*', 'version: %NEW_VERSION%' | Set-Content pubspec.yaml"

echo 🧹 flutter clean ^& get
call flutter clean
if errorlevel 1 (
    echo ❌ Flutter clean failed
    pause
    exit /b 1
)

call flutter packages get
if errorlevel 1 (
    echo ❌ Flutter packages get failed
    pause
    exit /b 1
)

echo 🚀 flutter build web --release
call flutter build web --release
if errorlevel 1 (
    echo ❌ Flutter build failed
    pause
    exit /b 1
)

REM Replace base href
echo 🔗 Updating ^<base href^> in index.html

SETLOCAL ENABLEEXTENSIONS

:: Check if BASE_HREF argument is provided
IF "%1"=="" (
    SET BASE_HREF=/nsl/nsl_new/
) ELSE (
    SET BASE_HREF=%1
)

:: Echo the value for verification (optional)
echo Using BASE_HREF: %BASE_HREF%

REM set BASE_HREF=/nsl/nsl_new/
powershell -command "(Get-Content build\web\index.html) -replace '<base href=\"/\">', '<base href=\"%BASE_HREF%\">' | Set-Content build\web\index.html"

REM Update main.dart.js references with version
echo 🔧 Patching main.dart.js references
powershell -command "(Get-Content build\web\flutter.js) -replace '\"main.dart.js\"', '\"main.dart.js?v=%VERSION_NO_PLUS%\"' | Set-Content build\web\flutter.js"
powershell -command "(Get-Content build\web\flutter_bootstrap.js) -replace '\"main.dart.js\"', '\"main.dart.js?v=%VERSION_NO_PLUS%\"' | Set-Content build\web\flutter_bootstrap.js"
powershell -command "(Get-Content build\web\index.html) -replace '\"main.dart.js\"', '\"main.dart.js?v=%VERSION_NO_PLUS%\"' | Set-Content build\web\index.html"

REM Patch main.dart.js for asset versioning
echo 🛠 Patching asset loader in main.dart.js
powershell -command "(Get-Content build\web\main.dart.js) -replace 'self\.window\.fetch\(a\),', 'self.window.fetch(a + \"?v=%VERSION_NO_PLUS%\"),' | Set-Content build\web\main.dart.js"

REM Add version to manifest.json
echo 📦 Adding version to manifest.json URL
powershell -command "(Get-Content build\web\index.html) -replace '\"manifest.json\"', '\"manifest.json?v=%VERSION_NO_PLUS%\"' | Set-Content build\web\index.html"

echo ✅ Flutter web build completed successfully!
echo 📦 New version: %NEW_VERSION%
pause