// To parse this JSON data, do
//
//     final nslSentences = nslSentencesFromJson(jsonString);

import 'dart:convert';

NslSentences nslSentencesFromJson(String str) => NslSentences.fromJson(json.decode(str));

String nslSentencesToJson(NslSentences data) => json.encode(data.toJson());

class NslSentences {
    List<Datum>? data;
    int? count;

    NslSentences({
        this.data,
        this.count,
    });

    NslSentences copyWith({
        List<Datum>? data,
        int? count,
    }) => 
        NslSentences(
            data: data ?? this.data,
            count: count ?? this.count,
        );

    factory NslSentences.fromJson(Map<String, dynamic> json) => NslSentences(
        data: json["data"] == null ? [] : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
        count: json["count"],
    );

    Map<String, dynamic> toJson() => {
        "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
        "count": count,
    };
}

class Datum {
    String? postgresName;
    String? postgresGroupname;
    String? javaName;
    DateTime? createTime;
    String? postgresDatatype;
    String? javaDatatype;
    String? lineNumbers;
    String? naturalLanguage;
    String? loId;

    Datum({
        this.postgresName,
        this.postgresGroupname,
        this.javaName,
        this.createTime,
        this.postgresDatatype,
        this.javaDatatype,
        this.lineNumbers,
        this.naturalLanguage,
        this.loId,
    });

    Datum copyWith({
        String? postgresName,
        String? postgresGroupname,
        String? javaName,
        DateTime? createTime,
        String? postgresDatatype,
        String? javaDatatype,
        String? lineNumbers,
        String? naturalLanguage,
        String? loId,
    }) => 
        Datum(
            postgresName: postgresName ?? this.postgresName,
            postgresGroupname: postgresGroupname ?? this.postgresGroupname,
            javaName: javaName ?? this.javaName,
            createTime: createTime ?? this.createTime,
            postgresDatatype: postgresDatatype ?? this.postgresDatatype,
            javaDatatype: javaDatatype ?? this.javaDatatype,
            lineNumbers: lineNumbers ?? this.lineNumbers,
            naturalLanguage: naturalLanguage ?? this.naturalLanguage,
            loId: loId ?? this.loId,
        );

    factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        postgresName: json["postgres_name"],
        postgresGroupname: json["postgres_groupname"],
        javaName: json["java_name"],
        createTime: json["create_time"] == null ? null : DateTime.parse(json["create_time"]),
        postgresDatatype: json["postgres_datatype"],
        javaDatatype: json["java_datatype"],
        lineNumbers: json["line_numbers"],
        naturalLanguage: json["natural_language"],
        loId: json["lo_id"],
    );

    Map<String, dynamic> toJson() => {
        "postgres_name": postgresName,
        "postgres_groupname": postgresGroupname,
        "java_name": javaName,
        "create_time": "${createTime!.year.toString().padLeft(4, '0')}-${createTime!.month.toString().padLeft(2, '0')}-${createTime!.day.toString().padLeft(2, '0')}",
        "postgres_datatype": postgresDatatype,
        "java_datatype": javaDatatype,
        "line_numbers": lineNumbers,
        "natural_language": naturalLanguage,
        "lo_id": loId,
    };
}
