import 'package:flutter/material.dart';

class SolutionTabProvider extends ChangeNotifier {
  int _selectedTabIndex = -1;
  bool _isBottomSheetOpen = false;

  int get selectedTabIndex => _selectedTabIndex;
  bool get isBottomSheetOpen => _isBottomSheetOpen;

  void selectTab(int index) {
    if (_selectedTabIndex != index) {
      _selectedTabIndex = index;
      notifyListeners();
    }
  }

  void clearSelection() {
    if (_selectedTabIndex != -1) {
      _selectedTabIndex = -1;
      notifyListeners();
    }
  }

  void setBottomSheetState(bool isOpen) {
    if (_isBottomSheetOpen != isOpen) {
      _isBottomSheetOpen = isOpen;
      notifyListeners();
    }
  }
}
