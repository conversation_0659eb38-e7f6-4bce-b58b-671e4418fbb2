import 'package:flutter/material.dart';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/models/workflow.dart';
import 'package:nsl/providers/solution_tab_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/screens/web_transaction/workflow_transaction_screen.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/flex_mapper.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/widget_factory.dart' as utils_widget_factory;
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/widgets/mobile/chat_input_field.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:convert';

class SolutionWidgets extends StatefulWidget {
  const SolutionWidgets({super.key});

  @override
  State<SolutionWidgets> createState() => _SolutionWidgetsState();
}

class _SolutionWidgetsState extends State<SolutionWidgets>
    with TickerProviderStateMixin {
  bool _hasRecommendationData() {
    if (userInterfaceData == null) return false;
    return userInterfaceData!.any((data) => data["type"] == "widget_1");
  }

  bool isAudioLoading = false;
  Map<String, dynamic>? jsonData;
  Map<String, dynamic>? solutionWidgetsData;
  List<Map<String, dynamic>>? userInterfaceData;
  List<Map<String, dynamic>> optionButtonsData = [];
  List<Map<String, dynamic>> recommendationData = [];
  List<Map<String, dynamic>> actionButtonsData = [];

  final List<String> _messages = [];
  final TextEditingController chatController = TextEditingController();
  final FocusNode _chatFocusNode = FocusNode();

  // Animation controllers
  late AnimationController _tabAnimationController;
  late AnimationController _bottomSheetAnimationController;
  late Animation<double> _tabScaleAnimation;

  int selectedTabIndex = -1;
  bool _isRecommendationExpanded = false;
  final double _recommendationMaxHeight = 400.0;
  final double _recommendationMinHeight = 200.0;
  bool _hasTransitioned = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _tabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _bottomSheetAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _tabScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _tabAnimationController,
      curve: Curves.easeInOut,
    ));

    _loadJsonData();
  }

  @override
  void dispose() {
    chatController.dispose();
    _chatFocusNode.dispose();
    _tabAnimationController.dispose();
    _bottomSheetAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadJsonData() async {
    try {
      final String jsonFile = _hasTransitioned
          ? 'assets/data/solution_lo_one.json'
          : 'assets/data/solution_lo_two.json';

      final String solutionJsonString = await rootBundle.loadString(jsonFile);
      final Map<String, dynamic> solutionData = jsonDecode(solutionJsonString);

      final String jsonString =
          await rootBundle.loadString('assets/data/solution_lo_two.json');
      final Map<String, dynamic> data = jsonDecode(jsonString);

      final String solutionWidgetsJsonString =
          await rootBundle.loadString('assets/data/solution_lo_two.json');
      final Map<String, dynamic> solutionWidgetsJsonData =
          jsonDecode(solutionWidgetsJsonString);

      setState(() {
        jsonData = data;
        solutionWidgetsData = solutionWidgetsJsonData;

        final String dataKey = _hasTransitioned
            ? 'lead_creation_lo2_information_hierarchy'
            : 'lead_creation_lo1_information_hierarchy';

        final leadCreationData = solutionData[dataKey];

        if (leadCreationData != null) {
          userInterfaceData = List<Map<String, dynamic>>.from(
              leadCreationData["physical_layer"]);

          if (_hasTransitioned) {
            final level2Context =
                leadCreationData['level_2_contextual_information'];
            final chatAreaCritical = level2Context?['chat_area_critical'];
            final opportunityHealthCheck =
                chatAreaCritical?['opportunity_health_check'];

            if (opportunityHealthCheck != null) {
              final title =
                  opportunityHealthCheck['title'] ?? '📈 Opportunity Health';
              final healthScore = opportunityHealthCheck['health_score'] ??
                  '82/100 - Strong Opportunity';

              List<String> cardContent = [];
              cardContent.add('$healthScore\n');

              final healthFactors =
                  opportunityHealthCheck['health_factors'] as List<dynamic>?;
              if (healthFactors != null) {
                for (var factor in healthFactors) {
                  cardContent.add(factor.toString());
                }
              }

              final improvements =
                  opportunityHealthCheck['improvement_recommendations']
                      as List<dynamic>?;
              if (improvements != null) {
                cardContent.add('\nImprovement Recommendations:');
                for (var improvement in improvements) {
                  cardContent.add('💡 ${improvement.toString()}');
                }
              }

              recommendationData = [
                {
                  'type': 'card',
                  'title': title,
                  'content': cardContent,
                  'icon': '📈',
                }
              ];
            } else {
              recommendationData = [
                {
                  'type': 'card',
                  'title': '📈 Opportunity Health',
                  'content': [
                    '82/100 - Strong Opportunity\n',
                    '✅ Budget: Likely allocated (based on funding)',
                    '⚠️ Authority: Partial - need to identify final decision maker',
                    '✅ Need: Strong pain points identified',
                    '✅ Timeline: Reasonable 3-6 month window',
                    '\nImprovement Recommendations:',
                    '💡 Identify CFO or budget holder',
                    '💡 Map complete stakeholder network',
                    '💡 Quantify business impact/ROI',
                  ],
                  'icon': '📈',
                }
              ];
            }
          } else {
            final level2Context =
                leadCreationData['level_2_contextual_information'];
            final chatAreaCritical = level2Context?['chat_area_critical'];

            if (chatAreaCritical != null) {
              final companyCard = chatAreaCritical['company_verification_card'];
              final territoryCard = chatAreaCritical['territory_fit_card'];

              recommendationData = [];

              if (companyCard != null) {
                recommendationData.add({
                  'type': 'card',
                  'title': companyCard['title'] ?? '✅ Company Verified',
                  'content': companyCard['content'] ?? [],
                  'action': companyCard['action'] ?? 'View full profile →',
                  'icon': '✅',
                });
              }

              if (territoryCard != null) {
                recommendationData.add({
                  'type': 'card',
                  'title': territoryCard['title'] ?? '🗺️ Territory Alignment',
                  'content': territoryCard['content'] ?? [],
                  'action':
                      territoryCard['action'] ?? 'View territory details →',
                  'icon': '🗺️',
                });
              }
            }
          }

          final level1Primary = leadCreationData['level_1_primary_actions'];
          final essentialInputs = level1Primary?['essential_inputs'];

          if (essentialInputs != null) {
            optionButtonsData = [
              {
                'type': 'card',
                'icon': 'assets/images/my_business/box_add.svg',
                'label': 'Verify Company',
                'onTap': () {
                  print("Verify Company clicked");
                },
              },
              {
                'type': 'card',
                'icon': 'assets/images/my_business/box_add.svg',
                'label': 'Check Territory',
                'onTap': () {
                  print("Check Territory clicked");
                },
              },
              {
                'type': 'card',
                'icon': 'assets/images/my_business/box_add.svg',
                'label': 'AI Enrichment',
                'onTap': () {
                  print("AI Enrichment clicked");
                },
              },
            ];
          }

          final progressionLogic = leadCreationData['progression_logic'];
          if (progressionLogic != null) {
            actionButtonsData = [
              {
                'text': 'Continue to Details →',
                'type': 'primary',
                'action': 'continue',
                'image': 'assets/images/my_business/solutions/send_sol.svg',
              },
              {
                'text': 'Save as Draft',
                'type': 'secondary',
                'action': 'save_draft',
                'image': 'assets/images/my_business/solutions/save_sol.svg',
              },
              {
                'text': 'Cancel',
                'type': 'secondary',
                'action': 'cancel',
                'image': 'assets/images/my_business/solutions/cancel_sol.svg',
              },
            ];
          }
        } else {
          _setDefaultData();
        }
      });

      print("JSON data loaded successfully");
    } catch (e) {
      print("Error loading JSON data: $e");
      _setDefaultData();
    }
  }

  void _setDefaultData() {
    optionButtonsData = [
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Check Policy',
        'onTap': () {
          print("Check Policy clicked");
        },
      },
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Add to Calendar',
        'onTap': () {
          print("Add to Calendar clicked");
        },
      },
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Use AI Suggestion',
        'onTap': () {
          print("AI Suggestion clicked");
        },
      },
    ];

    recommendationData = [
      {'type': 'text', 'value': '2-Day Overlap With John (Lead Developer)'},
      {'type': 'text', 'value': 'Manager Is Available For Approval All Week'},
      {
        'type': 'text',
        'value': 'You\'ll Have 12 Days Remaining After This Request'
      },
    ];
  }

  void _handleSendMessage() {
    final text = chatController.text.trim();
    if (text.isNotEmpty) {
      setState(() {
        _messages.add(text);
        chatController.clear();
      });
    }
  }

  String _buildDataAnalyticsContent(Map<String, dynamic>? dataAnalytics) {
    if (dataAnalytics == null) return "No data analytics available.";

    StringBuffer content = StringBuffer();

    final leaveAnalyticsCard = dataAnalytics['leave_analytics_card'];
    if (leaveAnalyticsCard != null) {
      content.writeln("${leaveAnalyticsCard['header'] ?? 'LEAVE ANALYTICS'}\n");

      final personalBalance = leaveAnalyticsCard['personal_balance'];
      if (personalBalance != null) {
        content.writeln("Personal Balance:");
        content.writeln(
            "• Current Usage: ${personalBalance['current_usage'] ?? 'N/A'}");
        content
            .writeln("• Percentage: ${personalBalance['percentage'] ?? 'N/A'}");
        content.writeln(
            "• Remaining After Request: ${personalBalance['remaining_after_request'] ?? 'N/A'}\n");
      }
    }

    final teamAvailabilityCard = dataAnalytics['team_availability_card'];
    if (teamAvailabilityCard != null) {
      content.writeln("TEAM AVAILABILITY\n");
      content.writeln(
          "Time Period: ${teamAvailabilityCard['time_period'] ?? 'N/A'}\n");

      final teamMembers =
          teamAvailabilityCard['team_members'] as List<dynamic>?;
      if (teamMembers != null) {
        content.writeln("Team Members:");
        for (var member in teamMembers) {
          content.writeln("• ${member['name']} (${member['role']})");
          content
              .writeln("  Availability: ${member['availability_percentage']}");
          content.writeln("  Status: ${member['status']}\n");
        }
      }
    }

    final conflictsCard = dataAnalytics['conflicts_warnings_card'];
    if (conflictsCard != null) {
      content.writeln("⚠️ CONFLICTS & WARNINGS\n");

      final conflicts =
          conflictsCard['high_priority_conflicts'] as List<dynamic>?;
      if (conflicts != null) {
        content.writeln("High Priority Conflicts:");
        for (var conflict in conflicts) {
          content.writeln(
              "• ${conflict['description']} (${conflict['severity']})");
        }
        content.writeln();
      }

      final aiSuggestion = conflictsCard['ai_suggestion'];
      if (aiSuggestion != null) {
        content.writeln("💡 AI Suggestion:");
        content.writeln("${aiSuggestion['recommendation'] ?? 'N/A'}\n");

        final benefits = aiSuggestion['benefits'] as List<dynamic>?;
        if (benefits != null) {
          content.writeln("Benefits:");
          for (var benefit in benefits) {
            content.writeln("• $benefit");
          }
        }
      }
    }

    return content.toString();
  }

  String _buildSystemArchitectureContent(
      Map<String, dynamic>? systemArchitecture) {
    if (systemArchitecture == null)
      return "No system architecture information available.";

    StringBuffer content = StringBuffer();
    content.writeln("🏗️ SYSTEM ARCHITECTURE\n");

    content.writeln(
        "${systemArchitecture['description'] ?? 'Backend infrastructure supporting the leave analytics dashboard'}\n");

    final realTimeProcessing = systemArchitecture['real_time_processing'];
    if (realTimeProcessing != null) {
      content.writeln("⚡ REAL-TIME PROCESSING\n");

      final dataSources = realTimeProcessing['data_sources'] as List<dynamic>?;
      if (dataSources != null) {
        content.writeln("Data Sources:");
        for (var source in dataSources) {
          content.writeln("• $source");
        }
        content.writeln();
      }

      final updateFreqs = realTimeProcessing['update_frequencies'];
      if (updateFreqs != null) {
        content.writeln("Update Frequencies:");
        updateFreqs.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
        content.writeln();
      }
    }

    final aiProcessing = systemArchitecture['ai_processing'];
    if (aiProcessing != null) {
      content.writeln("🤖 AI PROCESSING\n");

      final intentRecognition = aiProcessing['intent_recognition'];
      if (intentRecognition != null) {
        content.writeln("Intent Recognition:");
        content.writeln("• Input: ${intentRecognition['input'] ?? 'N/A'}");
        content.writeln(
            "• Confidence: ${intentRecognition['confidence'] ?? 'N/A'}\n");

        final parsedEntities = intentRecognition['parsed_entities'];
        if (parsedEntities != null) {
          content.writeln("Parsed Entities:");
          parsedEntities.forEach((key, value) {
            content.writeln("• ${key.toUpperCase()}: $value");
          });
          content.writeln();
        }
      }

      final suggestionEngine = aiProcessing['suggestion_engine'];
      if (suggestionEngine != null) {
        content.writeln("Suggestion Engine:");
        suggestionEngine.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
        content.writeln();
      }
    }

    final integrationPoints =
        systemArchitecture['integration_points'] as List<dynamic>?;
    if (integrationPoints != null) {
      content.writeln("🔗 INTEGRATION POINTS\n");
      for (var point in integrationPoints) {
        content.writeln("• $point");
      }
      content.writeln();
    }

    final performanceMetrics = systemArchitecture['performance_metrics'];
    if (performanceMetrics != null) {
      content.writeln("📈 PERFORMANCE METRICS\n");

      final responseTimes = performanceMetrics['response_times'];
      if (responseTimes != null) {
        content.writeln("Response Times:");
        responseTimes.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
        content.writeln();
      }

      final dataFreshness = performanceMetrics['data_freshness'];
      if (dataFreshness != null) {
        content.writeln("Data Freshness:");
        dataFreshness.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
      }
    }

    return content.toString();
  }

  bool _hasRelatedData() {
    return true;
    final leaveAnalytics = jsonData?['content_data'];
    final dataAnalytics = leaveAnalytics[0]?['data'];
    return dataAnalytics != null;
  }

  bool _hasContextualData() {
    return true;
    final leaveAnalytics = jsonData?['content_data'];
    final systemArchitecture = leaveAnalytics[1]?['data'];
    return systemArchitecture != null;
  }

  List<Map<String, String>> _getAvailableTabs() {
    List<Map<String, String>> availableTabs = [];

    if (_hasRelatedData()) {
      availableTabs.add({
        "label": "Related",
        "iconPath": 'assets/images/my_business/solutions/solution_related.svg',
      });
    }

    if (_hasContextualData()) {
      availableTabs.add({
        "label": "Contextual",
        "iconPath":
            'assets/images/my_business/solutions/solution_contextual.svg',
      });
    }

    return availableTabs;
  }

  Future<void> _showBottomSheet(String tabType, int tabIndex) async {
    // Animate tab press
    await _tabAnimationController.forward();
    await _tabAnimationController.reverse();

    // Don't set state immediately - wait for animation
    await Future.delayed(const Duration(milliseconds: 50));

    setState(() {
      selectedTabIndex = tabIndex;
    });

    if (!mounted) return;

    await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        barrierColor: Colors.black54,
        elevation: 0,
        enableDrag: true,
        isDismissible: true,
        builder: (context) => RightSectionWithTabs(
            isRelated: selectedTabIndex == 0
                ? false
                : selectedTabIndex == 1
                    ? true
                    : false)
        // TabContentBottomSheet(
        //   tabType: tabType,
        //   jsonData: jsonData,
        //   solutionWidgetsData: solutionWidgetsData,
        // ),
        );

    // Reset selection when bottom sheet is closed
    if (mounted) {
      setState(() {
        selectedTabIndex = -1;
      });
    }
  }

  void _showRecommendationBottomSheet(List<dynamic> recommendationDataList) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black54,
      elevation: 0,
      enableDrag: true,
      isDismissible: true,
      builder: (context) => RecommendationBottomSheet(
        recommendationData: recommendationDataList,
        iconPath: 'assets/images/my_business/solutions/recommendation_ai.svg',
      ),
    );
  }

  double _calculateRecommendationHeight() {
    double baseHeight = 60.0;
    int itemCount = optionButtonsData.length;
    double itemHeight = 40.0;
    double spacing = 12.0;
    int itemsPerRow = 2;
    int rows = (itemCount / itemsPerRow).ceil();
    double contentHeight = (rows * itemHeight) + ((rows - 1) * spacing) + 32.0;
    double totalHeight = baseHeight + contentHeight;
    return totalHeight > _recommendationMaxHeight
        ? _recommendationMaxHeight
        : totalHeight.clamp(_recommendationMinHeight, _recommendationMaxHeight);
  }

  Widget _buildRecommendationWidget(Map<String, dynamic> data) {
    final recommendationDataList =
        data['header']['recommendation']['data'] as List<dynamic>? ?? [];
    final title = data['header']['title']['value'] ?? 'Recommendation';
    final iconPath = data['header']['recommendation']['image'] ??
        'assets/images/my_business/solutions/recommendation_ai.svg';

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _showRecommendationBottomSheet(recommendationDataList),
        style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            elevation: 0,
            side: BorderSide(color: Colors.grey.shade300),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSpacing.xs),
            ),
            padding: const EdgeInsets.symmetric(vertical: AppSpacing.md, horizontal: AppSpacing.md),
            alignment: Alignment.centerLeft),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                SvgPicture.asset(
                  iconPath,
                  height: 16,
                  width: 16,
                ),
                SizedBox(width: AppSpacing.xs),
                Text(
                  title,
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.medium,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            // Icon(
            //   Icons.keyboard_arrow_up,
            //   color: Colors.grey.shade600,
            //   size: 20,
            // ),
          ],
        ),
      ),
    );
  }

  double _calculateDynamicRecommendationHeight(
      List<dynamic> recommendationData) {
    double baseHeight = 60.0;
    int itemCount = recommendationData.length;
    double itemHeight = 40.0;
    double spacing = 12.0;
    int itemsPerRow = 2;
    int rows = (itemCount / itemsPerRow).ceil();
    double contentHeight = (rows * itemHeight) + ((rows - 1) * spacing) + 32.0;
    double totalHeight = baseHeight + contentHeight;
    return totalHeight > _recommendationMaxHeight
        ? _recommendationMaxHeight
        : totalHeight.clamp(_recommendationMinHeight, _recommendationMaxHeight);
  }

  @override
  Widget build(BuildContext context) {
    final String latestMessage = _messages.isNotEmpty ? _messages.last : "";
    final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom > 0;

    return ChangeNotifierProvider(
      create: (context) => SolutionTabProvider(),
      child: NSLKnowledgeLoaderWrapper(
        isLoading: isAudioLoading,
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: Color(0xffF7F9FB),
          appBar: AppBar(
            surfaceTintColor: Colors.transparent,
            leading: Builder(
              builder: (context) => IconButton(
                icon: Icon(Icons.menu),
                onPressed: () {
                  Scaffold.of(context).openDrawer();
                },
              ),
            ),
            title: Text(''),
            backgroundColor: Color(0xffF7F9FB),
            elevation: 0,
            iconTheme: IconThemeData(color: Colors.black),
          ),
          drawer: CustomDrawer(),
          body: SafeArea(
            child: Column(
              children: [
                if (_getAvailableTabs().isNotEmpty)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.sm,
                    ),
                    child: ToggleTabs(
                      onTabTapped: _showBottomSheet,
                      availableTabs: _getAvailableTabs(),
                      selectedTabIndex: selectedTabIndex,
                      tabScaleAnimation: _tabScaleAnimation,
                    ),
                  ),
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: _getAvailableTabs().isNotEmpty
                              ? EdgeInsets.only(top: AppSpacing.sm)
                              : EdgeInsets.zero,
                          color: const Color(0xffF7F9FB),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Container(
                              constraints: BoxConstraints(
                                maxWidth:
                                    MediaQuery.of(context).size.width * 0.85,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFE9F2F7),
                                borderRadius: BorderRadius.circular(AppSpacing.xs),
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSpacing.md,
                                vertical: AppSpacing.xs,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  CircleAvatar(
                                    backgroundColor: const Color(0xFF0058FF),
                                    radius: 12,
                                    child: Text(
                                      'D',
                                      style: FontManager.getCustomStyle(
                                        color: Colors.white,
                                        fontSize: FontManager.s14,
                                        fontWeight: FontManager.regular,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: AppSpacing.xs),
                                  Flexible(
                                    child: Text(
                                      latestMessage.isNotEmpty
                                          ? latestMessage
                                          : "Type a message below...",
                                      style: FontManager.getCustomStyle(
                                        fontSize: FontManager.s14,
                                        fontWeight: FontManager.regular,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        WidgetComponent(uiData: userInterfaceData),
                      ],
                    ),
                  ),
                ),
                if (!isKeyboardOpen && _hasRecommendationData())
                  ...userInterfaceData!
                      .where((data) => data["type"] == "widget_1")
                      .map((data) => Container(
                            padding: EdgeInsets.only(
                                left: AppSpacing.sm,
                                right: AppSpacing.sm,
                                top: AppSpacing.xs),
                            child: _buildRecommendationWidget(data),
                          ))
                      .toList(),
                Padding(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom,
                      left: AppSpacing.sm,
                      right: AppSpacing.sm),
                  child: ChatInputField(
                    focusNode: _chatFocusNode,
                    chatController: chatController,
                    parentState: this,
                    sendMessage: _handleSendMessage,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class RecommendationBottomSheet extends StatefulWidget {
  final List<dynamic> recommendationData;
  final String iconPath;

  const RecommendationBottomSheet({
    super.key,
    required this.recommendationData,
    required this.iconPath,
  });

  @override
  State<RecommendationBottomSheet> createState() =>
      _RecommendationBottomSheetState();
}

class _RecommendationBottomSheetState extends State<RecommendationBottomSheet>
    with SingleTickerProviderStateMixin {
  late AnimationController _slideAnimationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOut,
    ));

    // Start animation immediately
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _slideAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _slideAnimationController.dispose();
    super.dispose();
  }

  double _calculateContentHeight() {
    // Base height for header and padding
    double baseHeight = 120.0;

    // Calculate height based on content
    int itemCount = widget.recommendationData.length;
    double itemHeight = 50.0; // Height per recommendation item
    double spacing = 12.0;

    // Calculate rows (assuming 1 item per row for recommendations)
    int rows = itemCount;
    double contentHeight = (rows * itemHeight) + ((rows - 1) * spacing);

    double totalHeight = baseHeight + contentHeight;

    // Max height should be 70% of screen height
    double maxHeight = MediaQuery.of(context).size.height * 0.9;

    return totalHeight > maxHeight ? maxHeight : totalHeight;
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _slideAnimationController,
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: DraggableScrollableSheet(
              initialChildSize: 0.95,
              minChildSize: 0.5,
              maxChildSize: 0.95,
              builder: (context, scrollController) => ClipRRect(
                borderRadius:
                    BorderRadius.vertical(top: Radius.circular(AppSpacing.lg)),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(
                        top: Radius.circular(AppSpacing.lg)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                            horizontal: AppSpacing.md, vertical: AppSpacing.xs),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          // borderRadius: BorderRadius.vertical(
                          //     top: Radius.circular(AppSpacing.lg)),
                        ),
                        child: Column(
                          children: [
                            Container(
                              width: 40,
                              height: 4,
                              margin: EdgeInsets.only(bottom: AppSpacing.sm),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade300,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    SvgPicture.asset(
                                      widget.iconPath,
                                      height: 20,
                                      width: 20,
                                    ),
                                    SizedBox(width: AppSpacing.xs),
                                    Text(
                                      'Recommendations',
                                      style: FontManager.getCustomStyle(
                                        fontSize: FontManager.s16,
                                        fontWeight: FontManager.semiBold,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                      ),
                                    ),
                                  ],
                                ),
                                IconButton(
                                  onPressed: () async {
                                    await _slideAnimationController.reverse();
                                    if (mounted) {
                                      Navigator.pop(context);
                                    }
                                  },
                                  icon: Icon(Icons.close, size: 24),
                                  // padding: EdgeInsets.all(8),
                                  constraints: BoxConstraints(
                                    minWidth: 40,
                                    minHeight: 40,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // Content
                      Expanded(
                        child: SingleChildScrollView(
                          controller: scrollController,
                          padding:
                              EdgeInsets.symmetric(horizontal: AppSpacing.md),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: AppSpacing.sm),
                              ...widget.recommendationData.map<Widget>((item) {
                                return Container(
                                  width: double.infinity,
                                  margin:
                                      EdgeInsets.only(bottom: AppSpacing.sm),
                                  padding: EdgeInsets.all(AppSpacing.xs),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade50,
                                    borderRadius:
                                        BorderRadius.circular(AppSpacing.sm),
                                    border:
                                        Border.all(color: Colors.grey.shade300),
                                  ),
                                  child: Row(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Color(0xFF0058FF)
                                              .withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(6),
                                        ),
                                        child: SvgPicture.asset(
                                          widget.iconPath,
                                          height: 16,
                                          width: 16,
                                          colorFilter: ColorFilter.mode(
                                            Color(0xFF0058FF),
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: AppSpacing.sm),
                                      Expanded(
                                        child: Text(
                                          item.toString(),
                                          style: FontManager.getCustomStyle(
                                            fontSize: FontManager.s14,
                                            fontFamily:
                                                FontManager.fontFamilyInter,
                                            fontWeight: FontManager.regular,
                                            color: Colors.grey.shade800,
                                            height: 1.4,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }).toList(),
                              SizedBox(height: AppSpacing.lg),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class ToggleTabs extends StatefulWidget {
  final Function(String, int) onTabTapped;
  final List<Map<String, String>> availableTabs;
  final int selectedTabIndex;
  final Animation<double> tabScaleAnimation;

  const ToggleTabs({
    super.key,
    required this.onTabTapped,
    required this.availableTabs,
    required this.selectedTabIndex,
    required this.tabScaleAnimation,
  });

  @override
  _ToggleTabsState createState() => _ToggleTabsState();
}

class _ToggleTabsState extends State<ToggleTabs> {
  int? _pressedIndex;

  void onTabTapped(int index) async {
    setState(() {
      _pressedIndex = index;
    });

    // Short delay to show press effect
    await Future.delayed(const Duration(milliseconds: 100));

    if (mounted) {
      setState(() {
        _pressedIndex = null;
      });

      widget.onTabTapped(widget.availableTabs[index]["label"]!, index);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.availableTabs.isEmpty) {
      return SizedBox.shrink();
    }

    return Row(
      children: List.generate(widget.availableTabs.length, (index) {
        final isSelected = widget.selectedTabIndex == index;
        final isPressed = _pressedIndex == index;
        final item = widget.availableTabs[index];

        return Expanded(
          child: AnimatedBuilder(
            animation: widget.tabScaleAnimation,
            builder: (context, child) {
              double scale = 1.0;
              if (isPressed) {
                scale = widget.tabScaleAnimation.value;
              }

              return Transform.scale(
                scale: scale,
                child: GestureDetector(
                  onTap: () => onTabTapped(index),
                  child: AnimatedContainer(
                    duration: Duration(milliseconds: 200),
                    curve: Curves.easeInOut,
                    margin: const EdgeInsets.symmetric(horizontal: AppSpacing.xxs),
                    padding:
                        const EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.xs),
                    decoration: BoxDecoration(
                      color: isSelected ? Color(0xFF0058FF) : Colors.white,
                      borderRadius: BorderRadius.circular(24),
                      border: Border.all(
                        color: isSelected
                            ? Color(0xFF0058FF)
                            : Colors.grey.shade300,
                        width: 1,
                      ),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: Colors.black12,
                                blurRadius: 8,
                                offset: Offset(0, 2),
                              )
                            ]
                          : [],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AnimatedSwitcher(
                          duration: Duration(milliseconds: 200),
                          child: SvgPicture.asset(
                            item["iconPath"]!,
                            key: ValueKey('${item["iconPath"]}_$isSelected'),
                            height: 18,
                            width: 18,
                            color: isSelected ? Colors.white : Colors.blue,
                          ),
                        ),
                        SizedBox(width: 6),
                        AnimatedDefaultTextStyle(
                          duration: Duration(milliseconds: 200),
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.black,
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                          child: Text(item["label"]!),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      }),
    );
  }
}

class WidgetComponent extends StatefulWidget {
  final List<Map<String, dynamic>>? uiData;

  const WidgetComponent({super.key, this.uiData});

  @override
  State<WidgetComponent> createState() => _WidgetComponentState();
}

class _WidgetComponentState extends State<WidgetComponent> {
  TextStyle? _getTextStyleFromJson(Map<String, dynamic>? textStyleJson) {
    if (textStyleJson == null) return null;

    double fontSize = 12.0;
    if (textStyleJson['fontsize'] != null) {
      fontSize = (textStyleJson['fontsize'] as num).toDouble();
    }

    FontWeight fontWeight = FontManager.medium;
    if (textStyleJson['fontweight'] != null) {
      final fontWeightStr = textStyleJson['fontweight'] as String;
      switch (fontWeightStr) {
        case 'w100':
          fontWeight = FontWeight.w100;
          break;
        case 'w200':
          fontWeight = FontWeight.w200;
          break;
        case 'w300':
          fontWeight = FontWeight.w300;
          break;
        case 'w400':
          fontWeight = FontWeight.w400;
          break;
        case 'w500':
          fontWeight = FontWeight.w500;
          break;
        case 'w600':
          fontWeight = FontWeight.w600;
          break;
        case 'w700':
          fontWeight = FontWeight.w700;
          break;
        case 'w800':
          fontWeight = FontWeight.w800;
          break;
        case 'w900':
          fontWeight = FontWeight.w900;
          break;
        default:
          fontWeight = FontManager.medium;
      }
    }

    Color textColor = Colors.black;
    if (textStyleJson['color'] != null) {
      final colorStr = textStyleJson['color'] as String;
      switch (colorStr.toLowerCase()) {
        case 'blue':
          textColor = Colors.blue;
          break;
        case 'red':
          textColor = Colors.red;
          break;
        case 'green':
          textColor = Colors.green;
          break;
        case 'orange':
          textColor = Colors.orange;
          break;
        case 'purple':
          textColor = Colors.purple;
          break;
        case 'black':
          textColor = Colors.black;
          break;
        case 'white':
          textColor = Colors.white;
          break;
        case 'grey':
          textColor = Colors.grey.shade600;
          break;
        default:
          textColor = Colors.black;
      }
    }
    final fontFamily = textStyleJson["fontfamily"];

    return FontManager.getCustomStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: textColor,
      fontFamily: fontFamily ?? FontManager.fontFamilyInter,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: AppSpacing.xs,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: (widget.uiData ?? []).map((e) => buildWidget(e)).toList(),
    );
  }

  Widget buildWidget(mapData) {
    if (mapData["user_input"] != null) {
      return uiWidget(mapData);
    } else {
      switch (mapData["type"]) {
        case "widget_1":
          return SizedBox.shrink();
        case "widget_2":
          return optionWidget(mapData);
        default:
          return Container();
      }
    }
  }

  Widget optionWidget(mapData) {
    List<Widget> widgets = (mapData['header']['options'] as List)
        .map<Widget>((e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        e['title']['value'] ?? '',
                        style: _getTextStyleFromJson(e['title']['style']) ??
                            FontManager.getCustomStyle(
                              fontSize: MediaQuery.of(context).size.width > 1550
                                  ? FontManager.s14
                                  : FontManager.s12,
                              fontFamily: FontManager.fontFamilyInter,
                              fontWeight: FontManager.medium,
                              color: Colors.black,
                            ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                if (e['data'] != null) ...[
                  ...((e['data'] as List<dynamic>).map<Widget>((contentItem) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        contentItem.toString(),
                        style: _getTextStyleFromJson(e['style']) ??
                            FontManager.getCustomStyle(
                              fontSize: MediaQuery.of(context).size.width > 1550
                                  ? FontManager.s13
                                  : FontManager.s10,
                              fontFamily: FontManager.fontFamilyInter,
                              fontWeight: FontManager.regular,
                              color: Colors.grey.shade700,
                            ),
                      ),
                    );
                  }).toList()),
                ],
                if (e['subtitle'] != null) ...[
                  const SizedBox(height: 8),
                  GestureDetector(
                    onTap: () {
                      print("${e['subtitle']} clicked");
                    },
                    child: Text(
                      e['subtitle']['value'] ?? '',
                      style: _getTextStyleFromJson(e['subtitle']['style']) ??
                          FontManager.getCustomStyle(
                            fontSize: FontManager.s12,
                            fontFamily: FontManager.fontFamilyInter,
                            fontWeight: FontManager.medium,
                            color: const Color(0xFF0058FF),
                          ),
                    ),
                  ),
                ],
                SizedBox(height: AppSpacing.sm),
              ],
            ))
        .toList();

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 1),
      ),
      child: Column(children: widgets),
    );
  }

  Widget uiWidget(mapData) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: AppSpacing.xs),
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.sm),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            mapData?['header']['title']['value'] ?? '',
            style:
                _getTextStyleFromJson(mapData?['header']['title']['style']) ??
                    FontManager.getCustomStyle(
                      fontSize: FontManager.s14,
                      fontWeight: FontManager.semiBold,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
          ),
          SizedBox(height: AppSpacing.xxs),
          if (mapData != null && mapData?["user_input"] != null)
            _buildFlexGrid(mapData!["user_input"])
          else
            const SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _buildFlexGrid(List<dynamic> inputList) {
    FlexMapper.setResponsiveFlex(context);

    List<Widget> rows = [];
    List<Widget> currentRow = [];
    int currentFlexSum = 0;

    for (int i = 0; i < inputList.length; i++) {
      final item = inputList[i];
      final uiControl = item['ui_control'] ?? 'text';
      final flex = FlexMapper.getFlexValueForControl(uiControl);

      if (currentFlexSum + flex > FlexMapper.totalRowFlex) {
        rows.add(
          Padding(
            padding: const EdgeInsets.only(bottom: AppSpacing.sm),
            child: Row(children: currentRow),
          ),
        );
        currentRow = [];
        currentFlexSum = 0;
      }

      final isFirstInRow = currentFlexSum == 0;
      final isLastInRow = (currentFlexSum + flex) == FlexMapper.totalRowFlex;

      final leftPadding = isFirstInRow ? 0.0 : AppSpacing.xs;
      final rightPadding = isLastInRow ? 0.0 : AppSpacing.xs;

      currentRow.add(
        Expanded(
          flex: flex,
          child: Padding(
            padding: EdgeInsets.only(
              left: leftPadding,
              right: rightPadding,
              top: AppSpacing.xxs,
              bottom: AppSpacing.xxs,
            ),
            child: buildUiWidget(item),
          ),
        ),
      );

      currentFlexSum += flex;
    }

    if (currentRow.isNotEmpty) {
      rows.add(
        Padding(
          padding: const EdgeInsets.only(bottom: AppSpacing.sm),
          child: Row(children: currentRow),
        ),
      );
    }

    return Column(children: rows);
  }

  Widget buildUiWidget(widgetData) {
    List? temp = widgetData["enum_values"]?.map((e) => e.toString()).toList();
    final InputField field = InputField(
      inputId: widgetData["item_id"] ?? '',
      inputStackId: widgetData["input_stack_id"] != null
          ? int.tryParse(widgetData["input_stack_id"]!)
          : null,
      attributeId: widgetData["attribute_id"] ?? '',
      entityId: widgetData["entity_id"] ?? '',
      displayName: widgetData["display_name"] ??
          widgetData["attribute_name"] ??
          'Unknown Field',
      dataType: widgetData["data_type"] ?? 'String',
      sourceType: widgetData["source_type"] ?? 'user',
      required: widgetData["required"] ?? false,
      uiControl: widgetData["ui_control"] ?? 'oj-input-text',
      isVisible: widgetData["is_visible"] ?? true,
      readOnly: widgetData["read_only"] ?? false,
      allowedValues: temp?.map((e) => e.toString()).toList(),
      validations: null,
      contextualId: widgetData["contextual_id"] ?? '',
      inputValue: widgetData["input_value"] ?? widgetData["default_value"],
      hasDropdownSource: widgetData["has_dropdown_source"] ?? false,
      dependencyType: widgetData["dependency_type"]?.toString(),
      metadata: InputFieldMetadata(
        usage: '',
        isInformational: widgetData["information_field"] ?? false,
        hasDropdownSource: widgetData["has_dropdown_source"] ?? false,
      ),
      dropdownOptions: null,
      needsParentValue: widgetData["needs_parent_value"],
      parentIds: widgetData["parent_ids"]?.map((id) => id.toString()).toList(),
    );

    return utils_widget_factory.WidgetFactory.createInputWidget(
      context: context,
      field: field,
      key: GlobalKey(debugLabel: "key_${field.displayName}"),
      sectionId: 'form',
      onValueChanged: (p0, p1, p2, p3) {},
    );
  }
}

class OptionComponent extends StatelessWidget {
  final Map<String, dynamic> data;

  const OptionComponent({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    switch (data['type']) {
      case 'card':
        return GestureDetector(
          onTap: data['onTap'],
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 1),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  data['icon'],
                  width: 16,
                  height: 16,
                  colorFilter: const ColorFilter.mode(
                      Color(0xFF0058FF), BlendMode.srcIn),
                ),
                const SizedBox(width: 8),
                Text(
                  data['label'],
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: FontManager.s12,
                    color: Colors.black,
                    fontWeight: FontManager.regular,
                  ),
                ),
              ],
            ),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }
}
class RightSectionWithTabs extends StatefulWidget {
  final Map<String, dynamic>? jsonData;
  final Map<String, dynamic>? solutionWidgetsData;
  bool isChatEnabled;
  bool isExpanded;
  Function? onExpansionChange;
  bool? isRelated;

  RightSectionWithTabs(
      {super.key,
      this.jsonData,
      this.solutionWidgetsData,
      this.isChatEnabled = false,
      this.isExpanded = false,
      this.onExpansionChange,
      this.isRelated = true});

  @override
  State<RightSectionWithTabs> createState() => _RightSectionWithTabsState();
}

class _RightSectionWithTabsState extends State<RightSectionWithTabs>
    with TickerProviderStateMixin {
  int selectedIndex = 0;
  List<Map<String, dynamic>> tabs = [];
  Map<String, dynamic>? tabsData;
  bool hasRightSideData = false;

  // Store the current tab's data for reordering
  List<dynamic> currentTabData = [];

  // Animation controllers for right section transition
  late AnimationController _rightSlideAnimationController;
  late Animation<Offset> _rightSlideAnimation;
  late AnimationController _rightFadeAnimationController;
  late Animation<double> _rightFadeAnimation;

  // State management for right section
  bool _isRightTransitioning = false;
  bool _hasRightTransitioned = false;

  late AnimationController _slideAnimationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeRightAnimations();
    _loadTabsData();
    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOut,
    ));

    // Start animation immediately
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _slideAnimationController.forward();
    });
  }

  void _initializeRightAnimations() {
    // Slide animation for right section content
    _rightSlideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _rightSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.0, -1.0),
    ).animate(CurvedAnimation(
      parent: _rightSlideAnimationController,
      curve: Curves.easeInOut,
    ));

    // Fade animation for smooth content transition
    _rightFadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _rightFadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _rightFadeAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _rightSlideAnimationController.dispose();
    _rightFadeAnimationController.dispose();
    _slideAnimationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant RightSectionWithTabs oldWidget) {
    // Check if parent has transitioned and trigger right section transition
    final bool parentHasTransitioned = _checkTransitionState();
    if (parentHasTransitioned != _hasRightTransitioned &&
        !_isRightTransitioning) {
      _triggerRightTransition();
    }
    super.didUpdateWidget(oldWidget);
  }

  Future<void> _triggerRightTransition() async {
    if (_isRightTransitioning) return;

    setState(() {
      _isRightTransitioning = true;
    });

    // Start fade out animation
    await _rightFadeAnimationController.forward();

    // Start slide-up animation
    await _rightSlideAnimationController.forward();

    // Update state and reload tabs data
    setState(() {
      _hasRightTransitioned = true;
      _isRightTransitioning = false;
    });

    // Reload tabs data with new state
    await _loadTabsData();

    // Reset animations for next use
    _rightSlideAnimationController.reset();
    _rightFadeAnimationController.reset();
  }

  // Check if tab data is available
  bool _hasTabData() {
    return tabs.isNotEmpty;
  }

  // Get available tabs based on data availability
  List<Map<String, dynamic>> _getAvailableTabs() {
    return tabs
        .where((tab) => tab['data'] != null && (tab['data'] as List).isNotEmpty)
        .toList();
  }

  Future<void> _loadTabsData() async {
    try {
      selectedIndex = !(widget.isRelated ?? false) ? 0 : 1;
      // Determine which JSON file to load based on transition state
      // Check if we're in the transitioned state by looking at the parent widget's state
      final bool hasTransitioned = _checkTransitionState();

      final String jsonFile = hasTransitioned
          ? 'assets/data/solution_lo_one.json'
          : 'assets/data/solution_lo_two.json';

      final String dataKey = hasTransitioned
          ? 'lead_creation_lo2_information_hierarchy'
          : 'lead_creation_lo1_information_hierarchy';

      // Load the appropriate JSON data
      final String jsonString = await rootBundle.loadString(jsonFile);
      final Map<String, dynamic> data = jsonDecode(jsonString);

      // Extract the right section tabs data
      final rightSectionData = data[dataKey]?['extra_details'];

      if (rightSectionData != null &&
          rightSectionData['content_data'] != null) {
        final List<dynamic> tabsList = rightSectionData['content_data'];
        tabs = tabsList
            .map((tab) => {
                  "id": tab['id'],
                  "title": tab["title"],
                  "data": List.from(tab["data"]), // Store raw tab data
                })
            .toList();
        hasRightSideData = true;
      } else {
        // No tab data available
        tabs = [];
        hasRightSideData = false;
      }

      setState(() {});
    } catch (e) {
      print("Error loading tabs data: $e");
      // Set empty tabs on error
      tabs = [];
      setState(() {});
    }
  }

  // Helper method to check transition state from parent context
  bool _checkTransitionState() {
    // Try to access the parent WebSolutionWidgets state to check transition
    // try {
    //   final context = this.context;
    //   final parentWidget =
    //       context.findAncestorWidgetOfExactType<WebSolutionWidgets>();
    //   if (parentWidget != null) {
    //     // Access the parent state through context
    //     final parentState =
    //         context.findAncestorStateOfType<_WebSolutionWidgetsState>();
    //     return parentState?._hasTransitioned ?? false;
    //   }
    // } catch (e) {
    //   print("Could not access parent transition state: $e");
    // }
    return false;
  }

  Widget buildTabData(List tabData, ValueChanged<List> onReorderComplete) {
    List<Map<String, dynamic>> currentTabData =
        List.from(tabData); // local copy

    return ListView.builder(
      physics: AlwaysScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: currentTabData.length,
        itemBuilder: (context, index) {
          final e = currentTabData[index];
          return Container(
            margin: EdgeInsets.only(bottom: AppSpacing.lg),
            child: buildTabWidget(e),
          );
        });
  }

  Widget buildTabWidget(Map<String, dynamic> tabWidget) {
    switch (tabWidget["type"]) {
      case "widget1":
        return widget1(tabWidget);
      case "widget2":
        return widget2(tabWidget);
      case "widget3":
        return widget3(tabWidget);
      case "widget4":
        return widget4(tabWidget);
      case "widget5":
        return widget5(tabWidget);
      case "widget6":
        return widget6(tabWidget);
      case "widget7":
        return widget7(tabWidget);
      case "widget8":
        return widget8(tabWidget);
      case "widget9":
        return widget9(tabWidget);
      case "widget10":
        return widget10(tabWidget);
      case "widget11":
        return widget11(tabWidget);
      case "widget12":
        return widget12(tabWidget);
      default:
        return Container();
    }
  }

  Widget widget1(Map<String, dynamic> tabWidgetData) {
    return companyProfileHeader(tabWidgetData);
  }

  Widget widget2(Map<String, dynamic> tabWidgetData) {
    final metrics = tabWidgetData["data"];
    return Column(children: [
      for (int i = 0; i < metrics.length; i += 2) ...[
        Row(
          children: [
            Expanded(
              child: _buildMetricCardFromJson(
                title: metrics[i]['title']["value"] ?? '',
                titleStyle: metrics[i]['title']["style"] ?? '',
                subtitle: metrics[i]['subtitle']["value"] ?? '',
                subtitleStyle: metrics[i]['subtitle']["style"] ?? '',
                tertiaryTitle: metrics[i]['tertiaryTitle']["value"] ?? '',
                tertiaryTitleStyle: metrics[i]['tertiaryTitle']["style"] ?? '',
              ),
            ),
            const SizedBox(width: 12),
            if (i + 1 < metrics.length)
              Expanded(
                child: _buildMetricCardFromJson(
                  title: metrics[i + 1]['title']["value"] ?? '',
                  titleStyle: metrics[i + 1]['title']["style"] ?? '',
                  subtitle: metrics[i + 1]['subtitle']["value"] ?? '',
                  subtitleStyle: metrics[i + 1]['subtitle']["style"] ?? '',
                  tertiaryTitle: metrics[i + 1]['tertiaryTitle']["value"] ?? '',
                  tertiaryTitleStyle:
                      metrics[i + 1]['tertiaryTitle']["style"] ?? '',
                ),
              )
            else
              Expanded(child: Container()),
          ],
        ),
        if (i + 2 < metrics.length) const SizedBox(height: 12),
      ],
    ]);
  }

  Widget widget3(Map<String, dynamic> tabWidgetData) {
    final techStack = tabWidgetData;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          techStack['title']['value'] ?? 'Technology Stack',
          style: _getTextStyleFromJson(techStack['title']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Wrap(
          spacing: AppSpacing.xs,
          runSpacing: AppSpacing.xs,
          children: (techStack['technologies']['data'] as List<dynamic>)
              .map((tech) => Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    child: Text(
                      tech.toString(),
                      style: _getTextStyleFromJson(
                              techStack['technologies']['style']) ??
                          FontManager.getCustomStyle(
                            fontSize: FontManager.s12,
                            fontWeight: FontManager.medium,
                            color: Colors.blue,
                          ),
                    ),
                  ))
              .toList(),
        ),
      ],
    );
  }

  Widget widget4(Map<String, dynamic> tabWidgetData) {
    final recentNews = tabWidgetData;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          recentNews['title']['value'] ?? 'Recent Company News',
          style: _getTextStyleFromJson(recentNews['title']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
        ),
        const SizedBox(height: 10),
        ...(recentNews['subtitle'] as List<dynamic>).map((newsItem) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildNewsItemFromJson(newsItem),
              const SizedBox(height: 4),
              Divider(thickness: 0.5, color: Colors.grey.shade300),
              // const SizedBox(height: 4),
            ],
          );
        }).toList(),
      ],
    );
  }

  Widget widget5(Map<String, dynamic> tabWidgetData) {
    final industryContext = tabWidgetData;
    return _buildMarketIntelCard(
      title: industryContext['title']['value'] ?? '',
      titleStyle: _getTextStyleFromJson(industryContext['title']['style']),
      children: [
        // Metrics from JSON
        if (industryContext['subtitle'] != null) ...[
          Row(
            children: [
              Expanded(
                child: _buildMarketMetricItemFromJson(
                    industryContext['subtitle'][0]),
              ),
              const SizedBox(width: 12),
              if (industryContext['subtitle'].length > 1)
                Expanded(
                  child: _buildMarketMetricItemFromJson(
                      industryContext['subtitle'][1]),
                )
              else
                Expanded(child: Container()),
            ],
          ),
          const SizedBox(height: 12),
        ],

        //   // Key Trends
        if (industryContext['tertiaryTitle'] != null) ...[
          Text(
            industryContext['tertiaryTitle']['value'] ?? '',
            style: _getTextStyleFromJson(
                    industryContext['tertiaryTitle']['style']) ??
                FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.semiBold,
                  color: Colors.black,
                ),
          ),
          const SizedBox(height: 6),
          ...(industryContext['tertiaryTitle']['data'] as List<dynamic>)
              .map((trend) {
            return _buildTrendItem(trend.toString(),
                industryContext['tertiaryTitle']['data_style']);
          }).toList(),
        ],
      ],
    );
  }

  Widget widget6(Map<String, dynamic> tabWidgetData) {
    final competitiveEnvironment = tabWidgetData;
    return _buildMarketIntelCard(
      title: competitiveEnvironment['title']['value'] ?? '',
      titleStyle:
          _getTextStyleFromJson(competitiveEnvironment['title']['style']),
      children: [
        //     // Competitors from JSON
        if (competitiveEnvironment['subtitle'] != null) ...[
          ...(competitiveEnvironment['subtitle'] as List<dynamic>)
              .map((competitor) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCompetitorItemFromJson(competitor),
                const SizedBox(height: 8),
                Text(
                  competitor['tertiaryTitle']['value'] ?? '',
                  style: _getTextStyleFromJson(
                          competitor['tertiaryTitle']['style']) ??
                      FontManager.getCustomStyle(
                        fontSize: FontManager.s10,
                        fontWeight: FontManager.regular,
                        color: Colors.grey.shade500,
                      ),
                ),
                const SizedBox(height: 12),
              ],
            );
          }).toList(),
        ],

        //     // Our Position
        if (competitiveEnvironment['tertiaryTitle'] != null) ...[
          Container(
            // padding: const EdgeInsets.only(left: 5,top: 10,bottom: 10,right: 5),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                Text(
                  competitiveEnvironment['tertiaryTitle']['title']['value'] ??
                      '',
                  style: _getTextStyleFromJson(
                          competitiveEnvironment['tertiaryTitle']['title']
                              ['style']) ??
                      FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontManager.regular,
                        color: Colors.black,
                      ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    competitiveEnvironment['tertiaryTitle']['subtitle']
                            ['value'] ??
                        '',
                    style: _getTextStyleFromJson(
                            competitiveEnvironment['tertiaryTitle']['subtitle']
                                ['style']) ??
                        FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.regular,
                          color: Colors.black,
                        ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
    // const SizedBox(height: 16),
  }

  Widget widget7(Map<String, dynamic> tabWidgetData) {
    final buyingSignals = tabWidgetData;
    return _buildMarketIntelCard(
      title: buyingSignals['title']['value'] ?? '',
      titleStyle: _getTextStyleFromJson(buyingSignals['title']['style']),
      children: [
        // Signals from JSON
        if (buyingSignals['subtitle'] != null) ...[
          Row(
            children: [
              Expanded(
                child: _buildSignalItemFromJson(buyingSignals['subtitle'][0]),
              ),
              const SizedBox(width: 12),
              if (buyingSignals['subtitle'].length > 1)
                Expanded(
                  child: _buildSignalItemFromJson(buyingSignals['subtitle'][1]),
                )
              else
                Expanded(child: Container()),
            ],
          ),
          const SizedBox(height: 16),
        ],

        // Intent Score
        if (buyingSignals['tertiaryTitle'] != null) ...[
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                buyingSignals['tertiaryTitle']['title']['value'] ??
                    'Intent Score',
                style: _getTextStyleFromJson(
                        buyingSignals['tertiaryTitle']['title']['style']) ??
                    FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.grey.shade600,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                buyingSignals['tertiaryTitle']['subtitle']['value'] ??
                    'Intent Score',
                style: _getTextStyleFromJson(
                        buyingSignals['tertiaryTitle']['subtitle']['style']) ??
                    FontManager.getCustomStyle(
                      fontSize: FontManager.s16,
                      fontWeight: FontManager.semiBold,
                      color: _parseColor(buyingSignals['tertiaryTitle']
                              ['subtitle']['style']['color']) ??
                          const Color(0xFF388E3C),
                    ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget widget8(Map<String, dynamic> tabWidgetData) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // West Coast Enterprise Header
          Container(
            padding: const EdgeInsets.all(12),
            // margin: EdgeInsets.only(top: AppSpacing.xs),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: const Color(0xFF0058FF),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          tabWidgetData['title']['value'] ?? '',
                          style: _getTextStyleFromJson(
                                  tabWidgetData['title']['style']) ??
                              FontManager.getCustomStyle(
                                fontSize: FontManager.s14,
                                fontWeight: FontManager.semiBold,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    if (tabWidgetData["hasExtraData"])
                      Container(
                        padding: const EdgeInsets.all(14),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFF3E0),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              tabWidgetData['extra']['title']['value'] ?? '',
                              style: _getTextStyleFromJson(
                                      tabWidgetData['extra']['title']
                                          ['style']) ??
                                  FontManager.getCustomStyle(
                                    fontSize: FontManager.s14,
                                    fontWeight: FontManager.semiBold,
                                    color: const Color(0xFFE65100),
                                  ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              tabWidgetData['extra']['subtitle']['value'] ?? '',
                              style: _getTextStyleFromJson(
                                      tabWidgetData['extra']['subtitle']
                                          ['style']) ??
                                  FontManager.getCustomStyle(
                                    fontSize: FontManager.s12,
                                    fontWeight: FontManager.regular,
                                    color: Colors.grey.shade700,
                                  ),
                            ),
                            // const SizedBox(height: 8),
                            Row(
                              children: [
                                Text(
                                  tabWidgetData['extra']['tertiaryTitle']
                                          ['value'] ??
                                      '',
                                  style: _getTextStyleFromJson(
                                          tabWidgetData['extra']
                                              ['tertiaryTitle']['style']) ??
                                      FontManager.getCustomStyle(
                                        fontSize: FontManager.s12,
                                        fontWeight: FontManager.medium,
                                        color: Colors.grey.shade700,
                                      ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            // Progress bar
                            Container(
                              height: 6,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade300,
                                borderRadius: BorderRadius.circular(3),
                              ),
                              child: FractionallySizedBox(
                                alignment: Alignment.centerLeft,
                                widthFactor: 0.68,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF4CAF50),
                                    borderRadius: BorderRadius.circular(3),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 20),

                // Account Metrics Grid
                Row(
                  children: [
                    Expanded(
                      child: _buildTerritoryMetricCard(
                          tabWidgetData['subtitle'][0]['title']['value'],
                          tabWidgetData['subtitle'][0]['subtitle']['value']),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildTerritoryMetricCard(
                          tabWidgetData['subtitle'][1]['title']['value'],
                          tabWidgetData['subtitle'][1]['subtitle']['value']),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                Row(
                  children: [
                    Expanded(
                      child: _buildTerritoryMetricCard(
                          tabWidgetData['subtitle'][2]['title']['value'],
                          tabWidgetData['subtitle'][2]['subtitle']['value']),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildTerritoryMetricCard(
                          tabWidgetData['subtitle'][3]['title']['value'],
                          tabWidgetData['subtitle'][3]['subtitle']['value']),
                    ),
                  ],
                ),

                // const SizedBox(height: 20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget widget9(Map<String, dynamic> tabWidgetData) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with LEAD SCORING and confidence
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE91E63),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    tabWidgetData['title']['value'] ?? '',
                    style: _getTextStyleFromJson(
                            tabWidgetData['title']['style']) ??
                        FontManager.getCustomStyle(
                          fontSize: FontManager.s14,
                          fontWeight: FontManager.semiBold,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                  ),
                ],
              ),
              Text(
                tabWidgetData['subtitle']['value'] ?? '',
                style:
                    _getTextStyleFromJson(tabWidgetData['subtitle']['style']) ??
                        FontManager.getCustomStyle(
                          fontSize: FontManager.s14,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFF4CAF50),
                        ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Large score display
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      // Apply consistent height for both web and tablet
                      constraints: const BoxConstraints(
                        minHeight: 70, // Consistent height for both platforms
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            tabWidgetData['tertiaryTitle']['value'] ?? '',
                            style: _getTextStyleFromJson(
                                    tabWidgetData['tertiaryTitle']['style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: 28,
                                  fontWeight: FontManager.semiBold,
                                  color: _parseColor(
                                          tabWidgetData['tertiaryTitle']
                                              ['style']['color']) ??
                                      const Color(0xFF4CAF50),
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      // Apply consistent height for both web and tablet
                      constraints: const BoxConstraints(
                        minHeight: 70, // Consistent height for both platforms
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            tabWidgetData['extraData']['title']['value'] ?? '',
                            style: _getTextStyleFromJson(
                                    tabWidgetData['extraData']['title']
                                        ['style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: FontManager.s14,
                                  fontWeight: FontManager.semiBold,
                                  color: Colors.black,
                                ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            tabWidgetData['extraData']['subtitle']['value'] ??
                                '',
                            style: _getTextStyleFromJson(
                                    tabWidgetData['extraData']['subtitle']
                                        ['style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: FontManager.s10,
                                  fontWeight: FontManager.regular,
                                  color: Colors.blue,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget widget10(Map<String, dynamic> tabWidgetData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          tabWidgetData['title']['value'] ?? '',
          style: _getTextStyleFromJson(tabWidgetData['title']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
        ),
        const SizedBox(height: 12),
        ...tabWidgetData['subtitle']
            .map((detail) => _buildCompactScoreItem(
                  detail['title']['value'],
                  detail['subtitle']['value'],
                  detail['data'],
                ))
            .toList(),
      ],
    );
  }

  Widget widget11(Map<String, dynamic> tabWidgetData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          tabWidgetData['title']['value'] ?? '',
          style: _getTextStyleFromJson(tabWidgetData['title']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
        ),
        const SizedBox(height: 8),
        ...tabWidgetData['data']
            .map(
              (detail) => _buildImprovementItem(detail),
            )
            .toList()
      ],
    );
  }

  Widget widget12(Map<String, dynamic> tabWidgetData) {
    final dealSizing = tabWidgetData['deal_sizing_intelligence'] ?? {};
    // final salesCycle = tabWidgetData['sales_cycle_prediction'] ?? {};
    // final winProbability = tabWidgetData['win_probability'] ?? {};
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Deal Sizing Intelligence Section
        _buildOpportunityIntelSection(
          title: dealSizing['title'] ?? 'Deal Sizing Intelligence',
          titleStyle: _getTextStyleFromJson(dealSizing['style']),
          child: _buildDealSizingContent(dealSizing),
        ),

        const SizedBox(height: 20),
      ],
    );
  }

  // Build Stakeholders content from JSON data
  Widget _buildStakeholdersContentFromJson(
      Map<String, dynamic> content, Map<String, dynamic>? textStyle) {
    final predictedStakeholders = content['predicted_stakeholders'] ?? {};
    final organizationalInsights = content['organizational_insights'] ?? {};

    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Predicted Stakeholder Network Section
          _buildStakeholdersSection(
            title: predictedStakeholders['title'] ??
                'Predicted Stakeholder Network',
            titleStyle:
                _getTextStyleFromJson(predictedStakeholders['title_style']),
            child: _buildStakeholderNetworkContent(predictedStakeholders),
          ),

          const SizedBox(height: 20),

          // Organizational Context Section
          _buildStakeholdersSection(
            title: organizationalInsights['title'] ?? 'Organizational Context',
            titleStyle:
                _getTextStyleFromJson(organizationalInsights['title_style']),
            child: _buildOrganizationalContextContent(organizationalInsights),
          ),
        ],
      ),
    );
  }

  Widget _buildStakeholdersSection({
    required String title,
    TextStyle? titleStyle,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Text(
            title,
            style: titleStyle ??
                FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.semiBold,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText),
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildStakeholderNetworkContent(
      Map<String, dynamic> stakeholdersData) {
    // Define the 4 stakeholders as shown in the UI
    final List<Map<String, dynamic>> stakeholders = [
      {
        'role': 'Chief Technology Officer',
        'influence': 'High',
        'concerns': ['Technical fit', 'Implementation complexity'],
        'strategy': 'Technical deep dive, reference calls',
      },
      {
        'role': 'VP Engineering',
        'influence': 'High',
        'concerns': ['Development impact', 'Tool efficiency'],
        'strategy': 'Developer experience demo',
      },
      {
        'role': 'CISO/Security Lead',
        'influence': 'Medium-High',
        'concerns': ['Security compliance', 'Data protection'],
        'strategy': 'Security docs, compliance overview',
      },
      {
        'role': 'CFO/Finance',
        'influence': 'High',
        'concerns': ['ROI', 'Cost justification'],
        'strategy': 'Business case, ROI analysis',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Stakeholder cards
        ...stakeholders.map((stakeholder) {
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Role title
                Text(
                  stakeholder['role'] ?? '',
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.semiBold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),

                // Influence level with proper styling
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getInfluenceColor(stakeholder['influence'] ?? '')
                        .withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${stakeholder['influence'] ?? ''} influence',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: _getInfluenceColor(stakeholder['influence'] ?? ''),
                    ),
                  ),
                ),
                const SizedBox(height: 12),

                // Concerns
                if (stakeholder['concerns'] != null) ...[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Concerns: ',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Colors.black,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          (stakeholder['concerns'] as List<dynamic>).join(', '),
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s12,
                            fontWeight: FontManager.regular,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                ],

                // Strategy
                if (stakeholder['strategy'] != null) ...[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Strategy: ',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Colors.black,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          stakeholder['strategy'] ?? '',
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s12,
                            fontWeight: FontManager.medium,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildOrganizationalContextContent(
      Map<String, dynamic> organizationalData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Company Structure and Decision Style in a row
        Row(
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Company Structure',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontManager.medium,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Engineering-driven',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        fontWeight: FontManager.medium,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Decision Style',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontManager.medium,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Consensus-based',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        fontWeight: FontManager.medium,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Cultural Factors
        Text(
          'Cultural Factors:',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.semiBold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),

        // Cultural factors list
        ...[
          'Innovation-focused culture',
          'Data-driven decision making',
          'Strong technical team autonomy'
        ].map((factor) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 6),
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    factor,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.regular,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Color _getInfluenceColor(String influence) {
    switch (influence.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium-high':
        return Colors.orange;
      case 'medium':
        return Colors.blue;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  // Build Competition content from JSON data
  Widget _buildCompetitionContentFromJson(
      Map<String, dynamic> content, Map<String, dynamic>? textStyle) {
    final competitiveLandscape = content['competitive_landscape'] ?? {};
    final differentiationStrategy = content['differentiation_strategy'] ?? {};

    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Competitive Analysis Section
          _buildCompetitionSection(
            title: competitiveLandscape['title'] ?? 'Competitive Analysis',
            titleStyle:
                _getTextStyleFromJson(competitiveLandscape['title_style']),
            child: _buildCompetitiveLandscapeContent(competitiveLandscape),
          ),

          const SizedBox(height: 20),

          // Key Differentiators Section
          _buildCompetitionSection(
            title: 'Key Differentiators',
            titleStyle: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontManager.semiBold,
              color: Colors.black,
            ),
            child: _buildKeyDifferentiatorsContent(),
          ),

          const SizedBox(height: 20),

          // Battle Plan Section
          _buildCompetitionSection(
            title: 'Battle Plan',
            titleStyle: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontManager.semiBold,
              color: Colors.black,
            ),
            child: _buildBattlePlanContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildCompetitionSection({
    required String title,
    TextStyle? titleStyle,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Text(
            title,
            style: titleStyle ??
                FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontWeight: FontManager.semiBold,
                  color: Colors.black,
                ),
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildCompetitiveLandscapeContent(
      Map<String, dynamic> competitiveData) {
    final competitors = competitiveData['competitors'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Competitor cards
        ...competitors.map((competitor) {
          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Competitor name and threat level
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      competitor['name'] ?? '',
                      style: _getTextStyleFromJson(competitor['name_style']) ??
                          FontManager.getCustomStyle(
                            fontSize: FontManager.s14,
                            fontWeight: FontManager.semiBold,
                            color: Colors.black,
                          ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getThreatColor(competitor['threat'] ?? '')
                            .withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        competitor['threat'] ?? '',
                        style:
                            _getTextStyleFromJson(competitor['threat_style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: FontManager.s12,
                                  fontWeight: FontManager.medium,
                                  color: _getThreatColor(
                                      competitor['threat'] ?? ''),
                                ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                IntrinsicHeight(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Text(
                            competitor['position'] ?? '',
                            textAlign: TextAlign.center,
                            style: _getTextStyleFromJson(
                                    competitor['position_style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: FontManager.s12,
                                  fontWeight: FontManager.semiBold,
                                  color: Colors.grey.shade600,
                                ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Text(
                            competitor['activity'] ?? '',
                            textAlign: TextAlign.center,
                            style: _getTextStyleFromJson(
                                    competitor['activity_style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: FontManager.s12,
                                  fontWeight: FontManager.regular,
                                  color: Colors.grey.shade600,
                                ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),

                // Our Advantages
                Text(
                  'Our Advantages:',
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s12,
                    fontWeight: FontManager.semiBold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 6),

                // Advantages list
                if (competitor['advantages'] != null) ...[
                  ...(competitor['advantages'] as List<dynamic>)
                      .map((advantage) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: const EdgeInsets.only(top: 6),
                            width: 4,
                            height: 4,
                            decoration: BoxDecoration(
                              color: Colors.black,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              advantage.toString(),
                              style: FontManager.getCustomStyle(
                                fontSize: FontManager.s12,
                                fontWeight: FontManager.regular,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildKeyDifferentiatorsContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Implementation Speed
        _buildDifferentiatorItem(
          'Implementation Speed',
          '2-3 weeks vs 2-3 months',
        ),
        const SizedBox(height: 12),

        // Integrations
        _buildDifferentiatorItem(
          'Integrations',
          '300+ out-of-box',
        ),
        const SizedBox(height: 12),

        // Customer Satisfaction
        _buildDifferentiatorItem(
          'Customer Satisfaction',
          '97% CSAT',
        ),
      ],
    );
  }

  Widget _buildDifferentiatorItem(String name, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          name,
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.medium,
            color: Colors.black,
          ),
        ),
        Text(
          value,
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.medium,
            color: Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildBattlePlanContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Key Messages
        Text(
          'Key Messages:',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.semiBold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),

        ...[
          'Emphasize rapid time-to-value',
          'Showcase integration ecosystem',
          'Highlight customer success stories',
          'Demonstrate scalability for growth'
        ].map((message) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 6),
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    message,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.regular,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),

        const SizedBox(height: 16),

        // Trap Questions
        Text(
          'Trap Questions:',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.semiBold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),

        ...[
          'Ask about implementation timeline concerns',
          'Discuss integration complexity challenges'
        ].map((question) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 6),
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    question,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.regular,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Color _getThreatColor(String threat) {
    switch (threat.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low-medium':
        return Colors.green;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Widget companyProfileHeader(
    Map<String, dynamic> content,
  ) {
    // Company Header
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Expanded(
              child: _reusable(
            showTitle: false,
            subtitle: content['title']["value"] ?? '',
            subtitleStyle: content['title']["style"] ?? '',
            tertiaryTitle: content['subtitle']["value"] ?? '',
            tertiaryTitleStyle: content['subtitle']["style"] ?? '',
          )),
        ],
      ),
    );
  }

  Widget _buildMetricCardFromJson({
    bool showTitle = true,
    String? title,
    Map<String, dynamic>? titleStyle,
    String? subtitle,
    Map<String, dynamic>? subtitleStyle,
    String? tertiaryTitle,
    Map<String, dynamic>? tertiaryTitleStyle,
  }) {
    final colorMap = {
      'blue': Colors.blue,
      'green': Colors.green,
      'purple': Colors.purple,
      'orange': Colors.orange,
    };

    return Container(
        padding: const EdgeInsets.all(12),
        constraints: !kIsWeb ? const BoxConstraints(minHeight: 120) : null,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: _reusable(
            title: title ?? '',
            titleStyle: titleStyle,
            subtitle: subtitle,
            subtitleStyle: subtitleStyle,
            tertiaryTitle: tertiaryTitle,
            tertiaryTitleStyle: tertiaryTitleStyle));
  }

  Widget _reusable({
    bool showTitle = true,
    String? title,
    Map<String, dynamic>? titleStyle,
    String? subtitle,
    Map<String, dynamic>? subtitleStyle,
    String? tertiaryTitle,
    Map<String, dynamic>? tertiaryTitleStyle,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment:
          !kIsWeb ? MainAxisAlignment.spaceBetween : MainAxisAlignment.start,
      children: [
        if (showTitle) ...[
          Text(
            title ?? '',
            style: _getTextStyleFromJson(titleStyle) ??
                FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.medium,
                  color: Colors.grey.shade600,
                ),
          ),
          const SizedBox(height: 6),
        ],
        Text(
          subtitle ?? '',
          style: _getTextStyleFromJson(subtitleStyle) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.semiBold,
                color: Colors.black,
              ),
        ),
        Text(
          tertiaryTitle ?? '',
          // metric['tertiaryTitle']['value'] ?? '',
          style: _getTextStyleFromJson(tertiaryTitleStyle
                  // metric['tertiaryTitle']['style']
                  ) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s10,
                fontWeight: FontManager.regular,
                color: Colors.orange,
              ),
        ),
      ],
    );
  }

  Widget _buildNewsItemFromJson(Map<String, dynamic> newsItem) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          newsItem['title']['value'] ?? '',
          style: _getTextStyleFromJson(newsItem['title']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.medium,
                color: Colors.black,
              ),
        ),
        const SizedBox(height: 4),
        Text(
          newsItem['subtitle']['value'] ?? '',
          style: _getTextStyleFromJson(newsItem['subtitle']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.medium,
                color: Colors.grey.shade600,
              ),
        ),
        const SizedBox(height: 4),
        Text(
          newsItem['tertiaryTitle']['value'] ?? '',
          style: _getTextStyleFromJson(newsItem['tertiaryTitle']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.medium,
                color: Colors.grey.shade600,
              ),
        ),
      ],
    );
  }

  // Build Opportunity Intel content from JSON data
  Widget _buildOpportunityIntelContentFromJson(
      Map<String, dynamic> content, Map<String, dynamic>? textStyle) {
    final dealSizing = content['deal_sizing_intelligence'] ?? {};
    final salesCycle = content['sales_cycle_prediction'] ?? {};
    final winProbability = content['win_probability'] ?? {};

    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Deal Sizing Intelligence Section
          _buildOpportunityIntelSection(
            title: dealSizing['title'] ?? 'Deal Sizing Intelligence',
            titleStyle: _getTextStyleFromJson(dealSizing['title_style']),
            child: _buildDealSizingContent(dealSizing),
          ),

          const SizedBox(height: 20),

          // Sales Cycle Prediction Section
          _buildOpportunityIntelSection(
            title: salesCycle['title'] ?? 'Sales Cycle Prediction',
            titleStyle: _getTextStyleFromJson(salesCycle['title_style']),
            child: _buildSalesCycleContent(salesCycle),
          ),

          const SizedBox(height: 20),

          // Win Probability Analysis Section
          _buildOpportunityIntelSection(
            title: winProbability['title'] ?? 'Win Probability Analysis',
            titleStyle: _getTextStyleFromJson(winProbability['title_style']),
            child: _buildWinProbabilityContent(winProbability),
          ),
        ],
      ),
    );
  }

  Widget _buildOpportunityIntelSection({
    required String title,
    TextStyle? titleStyle,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(kIsWeb ? 16 : 12), // Reduced padding for tablets
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title and key value - responsive layout
          kIsWeb
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: titleStyle ??
                            FontManager.getCustomStyle(
                                fontSize: FontManager.s14,
                                fontWeight: FontManager.semiBold,
                                color: Colors.black,
                                fontFamily: FontManager.fontFamilyTiemposText),
                      ),
                    ),
                    // Display key values based on section type

                    Text(
                      '\$125,000',
                      style: FontManager.getCustomStyle(
                        fontSize: 20,
                        fontWeight: FontManager.semiBold,
                        color: Colors.blue,
                      ),
                    ),

                    Text(
                      '4.1 months',
                      style: FontManager.getCustomStyle(
                        fontSize: 20,
                        fontWeight: FontManager.semiBold,
                        color: Colors.blue,
                      ),
                    ),

                    Text(
                      '76%',
                      style: FontManager.getCustomStyle(
                        fontSize: 20,
                        fontWeight: FontManager.semiBold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title on top for tablets
                    Text(
                      title,
                      style: titleStyle ??
                          FontManager.getCustomStyle(
                              fontSize:
                                  FontManager.s12, // Smaller font for tablets
                              fontWeight: FontManager.semiBold,
                              color: Colors.black,
                              fontFamily: FontManager.fontFamilyTiemposText),
                    ),
                    const SizedBox(height: 8),
                    // Key value below title for tablets
                    if (title.contains('Deal Sizing Intelligence'))
                      Text(
                        '\$125,000',
                        style: FontManager.getCustomStyle(
                          fontSize: 16, // Smaller font for tablets
                          fontWeight: FontManager.semiBold,
                          color: Colors.blue,
                        ),
                      )
                    else if (title.contains('Sales Cycle Prediction'))
                      Text(
                        '4.1 months',
                        style: FontManager.getCustomStyle(
                          fontSize: 16, // Smaller font for tablets
                          fontWeight: FontManager.semiBold,
                          color: Colors.blue,
                        ),
                      )
                    else if (title.contains('Win Probability Analysis'))
                      Text(
                        '76%',
                        style: FontManager.getCustomStyle(
                          fontSize: 16, // Smaller font for tablets
                          fontWeight: FontManager.semiBold,
                          color: Colors.green,
                        ),
                      ),
                  ],
                ),
          const SizedBox(height: 12),
          child,
        ],
      ),
    );
  }

  Widget _buildDealSizingContent(Map<String, dynamic> dealSizing) {
    final predictedDealSize = dealSizing['predicted_deal_size'] ?? {};
    final sizingFactors = dealSizing['sizing_factors'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Confidence and range information
        Text(
          '${predictedDealSize['confidence'] ?? '78% confidence'} \n${predictedDealSize['range'] ?? 'Range: \$85K - \$165K'}',
          style: _getTextStyleFromJson(predictedDealSize['confidence_style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.medium,
                color: Colors.grey.shade600,
              ),
        ),

        const SizedBox(height: 16),

        // Sizing factors in proper two-column layout
        Column(
          children: [
            // Company size factor row
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Company size factor',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    'Enterprise tier',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            // const SizedBox(height: 8),

            // Product interest row
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Product interest',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    'Full platform',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            // const SizedBox(height: 8),

            // Implementation row
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Implementation',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    'Medium complexity',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSalesCycleContent(Map<String, dynamic> salesCycle) {
    final predictedTimeline = salesCycle['predicted_timeline'] ?? {};
    final phaseBreakdown =
        salesCycle['phase_breakdown'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Confidence information
        Text(
          predictedTimeline['confidence'] ?? '84% confidence',
          style: _getTextStyleFromJson(predictedTimeline['confidence_style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.medium,
                color: Colors.grey.shade600,
              ),
        ),

        const SizedBox(height: 16),

        // Phase breakdown list
        ...phaseBreakdown.map((phase) {
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    phase['phase'] ?? '',
                    style: _getTextStyleFromJson(phase['phase_style']) ??
                        FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: Colors.black,
                        ),
                  ),
                ),
                Text(
                  phase['duration'] ?? '',
                  style: _getTextStyleFromJson(phase['duration_style']) ??
                      FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontManager.semiBold,
                        color: Colors.grey.shade600,
                      ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildWinProbabilityContent(Map<String, dynamic> winProbability) {
    final currentProbability = winProbability['current_probability'] ?? {};
    final probabilityFactors =
        winProbability['probability_factors'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label information
        Text(
          currentProbability['label'] ?? 'Current probability',
          style: _getTextStyleFromJson(currentProbability['label_style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.medium,
                color: Colors.grey.shade600,
              ),
        ),

        const SizedBox(height: 16),

        // Probability factors list
        ...probabilityFactors.map((factor) {
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    factor['factor'] ?? '',
                    style: _getTextStyleFromJson(factor['factor_style']) ??
                        FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.regular,
                          color: Colors.black,
                        ),
                  ),
                ),
                Text(
                  factor['impact'] ?? '',
                  style: _getTextStyleFromJson(factor['impact_style']) ??
                      FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontManager.semiBold,
                        color: Colors.red,
                      ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildMarketIntelCard({
    required String title,
    required List<Widget> children,
    TextStyle? titleStyle,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                title,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontWeight: FontManager.semiBold,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTrendItem(String trend, style) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 2),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade600,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            trend,
            style: _getTextStyleFromJson(style) ??
                FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.regular,
                  color: Colors.red,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildTerritoryMetricCard(String label, String value) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.medium,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontManager.semiBold,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactScoreItem(String title, String points, List details) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontWeight: FontManager.medium,
                  color: Colors.black,
                ),
              ),
              Text(
                points,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.medium,
                  color: const Color(0xFF4CAF50),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          ...details
              .map((detail) => Text(
                    detail,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s10,
                      fontWeight: FontManager.regular,
                      color: Colors.grey.shade600,
                    ),
                  ))
              .toList(),
          //   Divider(
          //   thickness: 1,
          //   color: Colors.grey.shade300,
          // ),
        ],
      ),
    );
  }

  Widget _buildImprovementItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.black,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.regular,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to convert JSON text style to Flutter TextStyle
  TextStyle? _getTextStyleFromJson(Map<String, dynamic>? textStyleJson) {
    if (textStyleJson == null) return null;

    // Parse font size from JSON
    double fontSize = 12.0;
    if (textStyleJson['fontsize'] != null) {
      fontSize = (textStyleJson['fontsize'] as num).toDouble();
    }

    // Parse font weight from JSON
    FontWeight fontWeight = FontManager.medium;
    if (textStyleJson['fontweight'] != null) {
      final fontWeightStr = textStyleJson['fontweight'] as String;
      switch (fontWeightStr) {
        case 'w100':
          fontWeight = FontWeight.w100;
          break;
        case 'w200':
          fontWeight = FontWeight.w200;
          break;
        case 'w300':
          fontWeight = FontWeight.w300;
          break;
        case 'w400':
          fontWeight = FontWeight.w400;
          break;
        case 'w500':
          fontWeight = FontWeight.w500;
          break;
        case 'w600':
          fontWeight = FontWeight.w600;
          break;
        case 'w700':
          fontWeight = FontWeight.w700;
          break;
        case 'w800':
          fontWeight = FontWeight.w800;
          break;
        case 'w900':
          fontWeight = FontWeight.w900;
          break;
        default:
          fontWeight = FontManager.medium;
      }
    }

    // Parse color from JSON
    Color textColor = Colors.black;
    if (textStyleJson['color'] != null) {
      final colorStr = textStyleJson['color'] as String;
      switch (colorStr.toLowerCase()) {
        case 'blue':
          textColor = Colors.blue;
          break;
        case 'red':
          textColor = Colors.red;
          break;
        case 'green':
          textColor = Colors.green;
          break;
        case 'orange':
          textColor = Colors.orange;
          break;
        case 'purple':
          textColor = Colors.purple;
          break;
        case 'black':
          textColor = Colors.black;
          break;
        case 'white':
          textColor = Colors.white;
          break;
        case 'grey':
          textColor = Colors.grey.shade600;
          break;
        default:
          textColor = Colors.black;
      }
    }

    return FontManager.getCustomStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: textColor,
      fontFamily: FontManager.fontFamilyInter,
    );
  }

  // Helper methods for Market Intel JSON parsing
  Widget _buildMarketMetricItemFromJson(Map<String, dynamic> tabWidgetData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _reusable(
          showTitle: false,
          subtitle: tabWidgetData['title']["value"] ?? '',
          subtitleStyle: tabWidgetData['title']["style"] ?? '',
          tertiaryTitle: tabWidgetData['subtitle']["value"] ?? '',
          tertiaryTitleStyle: tabWidgetData['subtitle']["style"] ?? '',
        ),
      ],
    );
  }

  Widget _buildCompetitorItemFromJson(Map<String, dynamic> competitor) {
    return Row(
      children: [
        Expanded(
          child: Text(
            competitor['title']['value'] ?? '',
            style: _getTextStyleFromJson(competitor['title']['style']) ??
                FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.regular,
                  color: Colors.black,
                ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color:
                _getThreatColor(competitor['subtitle']['style']['color'] ?? '')
                    .withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            competitor['subtitle']['value'] ?? '',
            style: _getTextStyleFromJson(competitor['subtitle']['style']) ??
                FontManager.getCustomStyle(
                  fontSize: FontManager.s10,
                  fontWeight: FontManager.medium,
                  color: _getThreatColor(
                      competitor['subtitle']['style']['color'] ?? ''),
                ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignalItemFromJson(Map<String, dynamic> buyingSignals) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _reusable(
            title: buyingSignals['title']["value"] ?? '',
            titleStyle: buyingSignals['title']["style"] ?? '',
            subtitle: buyingSignals['subtitle']["value"] ?? '',
            subtitleStyle: buyingSignals['subtitle']["style"] ?? '',
            tertiaryTitle: buyingSignals['tertiaryTitle']["value"] ?? '',
            tertiaryTitleStyle: buyingSignals['tertiaryTitle']["style"] ?? '',
          ),
        ],
      ),
    );
  }

  Color? _parseColor(String? colorString) {
    if (colorString == null) return null;

    // Handle hex colors
    if (colorString.startsWith('#')) {
      try {
        return Color(
            int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      } catch (e) {
        return null;
      }
    }

    // Handle named colors
    switch (colorString.toLowerCase()) {
      case 'red':
        return Colors.red;
      case 'green':
        return Colors.green;
      case 'blue':
        return Colors.blue;
      case 'orange':
        return Colors.orange;
      case 'purple':
        return Colors.purple;
      case 'black':
        return Colors.black;
      case 'white':
        return Colors.white;
      case 'grey':
        return Colors.grey;
      default:
        return null;
    }
  }

  // Helper method to get tab text style from JSON
  TextStyle _getTabTextStyle(
      Map<String, dynamic> tabData, bool isSelected, BuildContext context) {
    final textStyle = tabData['text_style'] as Map<String, dynamic>?;

    if (textStyle != null) {
      // Get the appropriate style based on selection state
      final styleToUse = isSelected
          ? (textStyle['selected'] as Map<String, dynamic>? ??
              textStyle['default'] as Map<String, dynamic>?)
          : textStyle['default'] as Map<String, dynamic>?;

      if (styleToUse != null) {
        // Parse font size from JSON
        double fontSize = 12.0;
        if (styleToUse['fontsize'] != null) {
          fontSize = (styleToUse['fontsize'] as num).toDouble();
        }

        // Parse font weight from JSON
        FontWeight fontWeight = FontManager.medium;
        if (styleToUse['fontweight'] != null) {
          final fontWeightStr = styleToUse['fontweight'] as String;
          switch (fontWeightStr) {
            case 'w100':
              fontWeight = FontWeight.w100;
              break;
            case 'w200':
              fontWeight = FontWeight.w200;
              break;
            case 'w300':
              fontWeight = FontWeight.w300;
              break;
            case 'w400':
              fontWeight = FontWeight.w400;
              break;
            case 'w500':
              fontWeight = FontWeight.w500;
              break;
            case 'w600':
              fontWeight = FontWeight.w600;
              break;
            case 'w700':
              fontWeight = FontWeight.w700;
              break;
            case 'w800':
              fontWeight = FontWeight.w800;
              break;
            case 'w900':
              fontWeight = FontWeight.w900;
              break;
            default:
              fontWeight = FontManager.medium;
          }
        }

        // Parse color from JSON
        Color textColor = Colors.black;
        if (styleToUse['color'] != null) {
          final colorStr = styleToUse['color'] as String;
          switch (colorStr.toLowerCase()) {
            case 'blue':
              textColor = Colors.blue;
              break;
            case 'red':
              textColor = Colors.red;
              break;
            case 'green':
              textColor = Colors.green;
              break;
            case 'orange':
              textColor = Colors.orange;
              break;
            case 'purple':
              textColor = Colors.purple;
              break;
            case 'black':
              textColor = Colors.black;
              break;
            case 'white':
              textColor = Colors.white;
              break;
            case 'grey':
              textColor = Colors.grey;
              break;
            default:
              textColor = Colors.black;
          }
        }

        return FontManager.getCustomStyle(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: textColor,
          fontFamily: FontManager.fontFamilyInter,
        );
      }
    }

    // Fallback to default styling if no text_style in JSON
    return FontManager.getCustomStyle(
      fontSize: MediaQuery.of(context).size.width > 1550
          ? FontManager.s14
          : FontManager.s12,
      fontWeight: isSelected ? FontManager.semiBold : FontManager.medium,
      color: Colors.black,
      fontFamily: FontManager.fontFamilyInter,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _slideAnimationController,
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: DraggableScrollableSheet(
              initialChildSize: 0.95,
              minChildSize: 0.3,
              maxChildSize: 0.95,
              builder: (context, scrollController) => ClipRRect(
                borderRadius:
                    BorderRadius.vertical(top: Radius.circular(AppSpacing.lg)),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(
                        top: Radius.circular(AppSpacing.lg)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                            horizontal: AppSpacing.md, vertical: AppSpacing.xs),
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                        child: Column(
                          children: [
                            Container(
                              width: 40,
                              height: 4,
                              margin: EdgeInsets.only(bottom: AppSpacing.sm),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade300,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "",
                                  // widget.tabType,
                                  style: FontManager.getCustomStyle(
                                    fontSize: FontManager.s16,
                                    fontWeight: FontManager.semiBold,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                  ),
                                ),
                                IconButton(
                                  onPressed: () async {
                                    await _slideAnimationController.reverse();
                                    if (mounted) {
                                      Navigator.pop(context);
                                    }
                                  },
                                  icon: Icon(Icons.close, size: 24),
                                  constraints: BoxConstraints(
                                    minWidth: 40,
                                    minHeight: 40,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // Content
                      Expanded(
                        child: SingleChildScrollView(
                          controller: scrollController,
                          padding:
                              EdgeInsets.symmetric(horizontal: AppSpacing.md ),
                          child: SizedBox(
                            height: MediaQuery.of(context).size.height*0.8,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: AppSpacing.sm),
                                Expanded(
                                  child: selectedIndex <= tabs.length - 1 &&
                                          tabs[selectedIndex].containsKey("data")
                                      ? buildTabData(
                                          tabs[selectedIndex]["data"],
                                          (List newOrder) {
                                            setState(() {
                                              tabs[selectedIndex]["data"] =
                                                  newOrder;
                                            });
                                          },
                                        )
                                      : Text(
                                          tabs[selectedIndex]["content"] as String,
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                MediaQuery.of(context).size.width >
                                                        1550
                                                    ? FontManager.s16
                                                    : FontManager.s14,
                                            fontFamily: FontManager.fontFamilyInter,
                                          ),
                                        ),
                                ),
                                SizedBox(height: AppSpacing.lg),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );

  }
}

class _HoverableReorderItem extends StatefulWidget {
  final Widget child;
  final int index;

  const _HoverableReorderItem({
    Key? key,
    required this.child,
    required this.index,
  }) : super(key: key);

  @override
  State<_HoverableReorderItem> createState() => _HoverableReorderItemState();
}

class _HoverableReorderItemState extends State<_HoverableReorderItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Stack(
        children: [
          Container(
            margin: EdgeInsets.only(bottom: AppSpacing.lg),
            child: widget.child,
          ),
          if (isHovered)
            Positioned(
              top: 8,
              right: 8,
              child: ReorderableDragStartListener(
                index: widget.index,
                child: CustomImage.asset(
                  "assets/images/my_business/expand_collection.svg",
                  width: 18,
                  height: 18,
                ).toWidget(),
              ),
            ),
        ],
      ),
    );
  }
}
