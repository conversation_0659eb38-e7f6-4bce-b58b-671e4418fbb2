import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/global_objectives_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/models/books/all_global_objective_model.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class TabletPortraitSolutions extends StatefulWidget {
  final String? jsonDataPath;

  const TabletPortraitSolutions({
    super.key,
    this.jsonDataPath = 'assets/data/collections.json',
  });

  @override
  State<TabletPortraitSolutions> createState() => _TabletPortraitSolutionsState();
}

class _TabletPortraitSolutionsState extends State<TabletPortraitSolutions> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _isLoading = true;
  List<Objective> _objectives = [];
  List<Objective> _filteredObjectives = [];

  // Search functionality
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchBar = false;

  // Pagination
  static const int itemsPerPage = 15; // 5 items per row, 3 rows per page for tablet
  int _currentCollectionPage = 1;
  int _totalCollectionPages = 1;

  // Sample banner images
  final List<String> bannerImages = [
    'assets/images/my_business/collections/my_business_carousel_one.jpg',
    'assets/images/my_business/collections/my_business_carousel_two.jpg',
    'assets/images/my_business/collections/my_business_carousel_three.jpg',
  ];
  final CarouselSliderController carouselController = CarouselSliderController();
  bool isHovered = false;

  @override
  void initState() {
    super.initState();
    _loadObjectivesData();

    // Initialize filtered objectives
    _filteredObjectives = List.from(_objectives);

    // Initialize pagination
    _currentCollectionPage = 1;
    _updateTotalPages();

    // Add listener to search controller
    _searchController.addListener(_filterObjectives);
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterObjectives);
    _searchController.dispose();
    super.dispose();
  }

  // Filter objectives based on search text and update pagination
  void _filterObjectives() {
    final searchText = _searchController.text.toLowerCase();

    setState(() {
      if (searchText.isEmpty) {
        _filteredObjectives = List.from(_objectives);
      } else {
        _filteredObjectives = _objectives
            .where((objective) =>
                (objective.name?.toLowerCase().contains(searchText) ?? false) ||
                (objective.description?.toLowerCase().contains(searchText) ?? false))
            .toList();
      }

      // Reset to first page when filtering
      _currentCollectionPage = 1;
      _updateTotalPages();
    });
  }

  // Toggle search bar visibility
  void _toggleSearchBar() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        // Clear search when hiding search bar
        _searchController.clear();
      }
    });
  }

  // Update total pages based on filtered objectives
  void _updateTotalPages() {
    _totalCollectionPages = (_filteredObjectives.length / itemsPerPage).ceil();
    if (_totalCollectionPages < 1) _totalCollectionPages = 1;
  }

  // Navigate to previous page
  void _previousPage() {
    if (_currentCollectionPage > 1) {
      setState(() {
        _currentCollectionPage--;
      });
    }
  }

  // Navigate to next page
  void _nextPage() {
    if (_currentCollectionPage < _totalCollectionPages) {
      setState(() {
        _currentCollectionPage++;
      });
    }
  }

  // Get current page items
  List<Objective> _getCurrentPageItems() {
    if (_filteredObjectives.isEmpty) return [];

    final startIndex = (_currentCollectionPage - 1) * itemsPerPage;
    final endIndex = startIndex + itemsPerPage;

    if (startIndex >= _filteredObjectives.length) return [];

    return _filteredObjectives.sublist(
        startIndex,
        endIndex > _filteredObjectives.length
            ? _filteredObjectives.length
            : endIndex);
  }

  /// Load objectives data from Global Objectives API
  Future<void> _loadObjectivesData() async {
    try {
      // Define fallback data in case API loading fails
      final List<Objective> fallbackData = [
        Objective(
          goId: "obj_1",
          name: "Apply Leave",
          description: "Process for applying leave",
          status: "active",
        ),
        Objective(
          goId: "obj_2",
          name: "Employee Details",
          description: "View and manage employee information",
          status: "active",
        ),
        Objective(
          goId: "obj_3",
          name: "My completed Module",
          description: "Track completed training modules",
          status: "active",
        ),
        Objective(
          goId: "obj_4",
          name: "My Attendance",
          description: "View attendance records",
          status: "active",
        ),
        Objective(
          goId: "obj_5",
          name: "Organisation Policy Details",
          description: "Access company policies",
          status: "active",
        ),
        Objective(
          goId: "obj_6",
          name: "Product Details",
          description: "View product information",
          status: "active",
        ),
        Objective(
          goId: "obj_7",
          name: "Payment Refund",
          description: "Process payment refunds",
          status: "active",
        ),
        Objective(
          goId: "obj_8",
          name: "My Orders",
          description: "Track order history",
          status: "active",
        ),
        Objective(
          goId: "obj_9",
          name: "My Wallet",
          description: "Manage wallet balance",
          status: "active",
        ),
        Objective(
          goId: "obj_10",
          name: "Return Policy",
          description: "View return policy details",
          status: "active",
        ),
      ];

      setState(() {
        _objectives = fallbackData;
        _filteredObjectives = List.from(_objectives);
        _updateTotalPages();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error in _loadObjectivesData: $e');
      setState(() {
        _objectives = [];
        _filteredObjectives = [];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      body: SafeArea(
        child: _buildPortraitBody(context),
      ),
    );
  }

  Widget _buildPortraitBody(BuildContext context) {
    return SingleChildScrollView(
      child: Row(
        children: [
        
          Expanded(
            flex: 3, // Same flex structure as web version
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: AppSpacing.md),
              child: Column(
                children: [
                  // Banner Slider
                  SizedBox(height: AppSpacing.sm),
                  _buildCarouselSlider(),
                  
                  // Dot Indicator
                  _buildDotIndicator(),
                  
                  SizedBox(height: AppSpacing.md),
                  
                  // Solutions title & search
                  _buildSolutionsHeader(),
                  
                  SizedBox(height: AppSpacing.md),
                  
                  // Loading indicator or grid
                  _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : _buildSolutionsGrid(),
                  
                  // Pagination controls
                  _buildPaginationControls(),
                ],
              ),
            ),
          ),
         
        ],
      ),
    );
  }

  Widget _buildCarouselSlider() {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Stack(
        children: [
          CarouselSlider.builder(
            itemCount: bannerImages.length,
            itemBuilder: (context, index, realIndex) {
              return ClipRRect(
                borderRadius: BorderRadius.circular(AppSpacing.sm),
                child: Image.asset(
                  bannerImages[index],
                  fit: BoxFit.cover,
                  width: double.infinity,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: double.infinity,
                      height: 180,
                      color: Colors.grey[200],
                      child: Center(
                        child: Icon(
                          Icons.broken_image,
                          color: Colors.grey[400],
                          size: 48,
                        ),
                      ),
                    );
                  },
                ),
              );
            },
            options: CarouselOptions(
              height: MediaQuery.of(context).size.height / 6, // Adjusted for tablet
              viewportFraction: 1.0,
              autoPlay: true,
              autoPlayInterval: Duration(seconds: 5),
              onPageChanged: (index, reason) {
                setState(() {
                  _currentPage = index;
                });
              },
            ),
            carouselController: carouselController,
          ),

          // Left arrow (only on hover)
          if (isHovered)
            Positioned(
              left: 8,
              top: 0,
              bottom: 0,
              child: Center(
                child: InkWell(
                  onTap: () {
                    carouselController.previousPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.ease,
                    );
                  },
                  child: CustomImage.asset(
                    "assets/images/my_business/collections/left_carousel.svg",
                  ).toWidget(),
                ),
              ),
            ),

          // Right arrow (only on hover)
          if (isHovered)
            Positioned(
              right: 8,
              top: 0,
              bottom: 0,
              child: Center(
                child: InkWell(
                  onTap: () {
                    carouselController.nextPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.ease,
                    );
                  },
                  child: CustomImage.asset(
                    "assets/images/my_business/collections/right_carousel.svg",
                  ).toWidget(),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDotIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          bannerImages.length,
          (index) => Container(
            margin: const EdgeInsets.symmetric(horizontal: AppSpacing.xxs),
            width: _currentPage == index ? 20 : 8,
            height: 8,
            decoration: BoxDecoration(
              color: _currentPage == index ? Colors.blue : Colors.grey,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSolutionsHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          AppLocalizations.of(context).translate('myBusinessSolutions.solutions'),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyLarge(context),
            fontWeight: FontManager.medium,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        _showSearchBar
            ? SearchBarWidget(
                controller: _searchController,
                onClose: _toggleSearchBar,
              )
            : GestureDetector(
                onTap: _toggleSearchBar,
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: Container(
                    height: 36,
                    child: HoverableSearchIcon(),
                  ),
                ),
              ),
      ],
    );
  }

  Widget _buildSolutionsGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 5, // 5 items per row for tablet portrait
        crossAxisSpacing: AppSpacing.sm,
        mainAxisSpacing: AppSpacing.sm,
        childAspectRatio: 0.75, // Adjusted for tablet cards
      ),
      itemCount: _getCurrentPageItems().length,
      itemBuilder: (context, index) {
        final objective = _getCurrentPageItems()[index];
        return TabletObjectiveCard(
          objective: objective,
        );
      },
    );
  }
Widget _buildPaginationControls() {
  // Only show pagination if there are more than 15 items (multiple pages)
  if (_totalCollectionPages <= 1) {
    return SizedBox.shrink(); // Hide pagination when only one page
  }
  
  return Container(
    margin: const EdgeInsets.symmetric(vertical: AppSpacing.lg),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Previous button
        _HoverPaginationButton(
          icon: const Icon(Icons.chevron_left, size: 20),
          onPressed: _currentCollectionPage > 1 ? () => _previousPage() : null,
        ),
        const SizedBox(width: 8),
        // Next button
        _HoverPaginationButton(
          icon: const Icon(Icons.chevron_right, size: 20),
          onPressed: _currentCollectionPage < _totalCollectionPages
              ? () => _nextPage()
              : null,
        ),
      ],
    ),
  );
}
}

class TabletObjectiveCard extends StatefulWidget {
  final Objective objective;

  const TabletObjectiveCard({
    Key? key,
    required this.objective,
  }) : super(key: key);

  @override
  _TabletObjectiveCardState createState() => _TabletObjectiveCardState();
}

class _TabletObjectiveCardState extends State<TabletObjectiveCard> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: () {
          final provider = Provider.of<WebHomeProvider>(context, listen: false);
          provider.currentScreenIndex = ScreenConstants.webSolutionWidgets;
          provider.solutionWidgetsSelectedFrom = ScreenConstants.myBusinessSolutions;
          provider.selectedSolutionName = widget.objective.name ?? '';
        },
        child: Card(
          color: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSpacing.xs),
            side: BorderSide(
              color: isHovered ? const Color(0xff0058FF) : const Color(0xffD0D0D0),
              width: isHovered ? 1 : 0.5,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.xs),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Image section
                Expanded(
                  flex: 3,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: CustomImage.asset(
                      'assets/images/my_business/solutions/solution_apply.png',
                    ).toWidget(fit: BoxFit.cover),
                  ),
                ),

                // Spacing
                SizedBox(height: AppSpacing.xxs),

                // Title
                Expanded(
                  flex: 1,
                  child: Container(
                    alignment: Alignment.topLeft,
                    child: Text(
                      widget.objective.name ?? 'Unknown Objective',
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: ResponsiveFontSizes.titleSmall(context),
                        fontWeight: FontManager.regular,
                        color: Colors.black,
                        height: 1.2,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.left,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class SearchBarWidget extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onClose;

  const SearchBarWidget({
    super.key,
    required this.controller,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 250,
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // Search input field
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: FontManager.getCustomStyle(
                  color: Colors.grey.shade500,
                  fontSize: FontManager.s14,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0),
                isDense: true,
                hoverColor: Colors.transparent,
              ),
              style: FontManager.getCustomStyle(fontSize: FontManager.s14),
              autofocus: true,
            ),
          ),

          // Close button
          GestureDetector(
            onTap: onClose,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: Icon(Icons.close, size: 18, color: Colors.grey.shade600),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.onPressed == null
                ? Colors.grey.shade200
                : (isHovered ? Color(0xff0058FF) : Colors.grey.shade300),
            width: 1.0,
          ),
          borderRadius: isHovered ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: widget.onPressed == null
              ? Colors.grey.shade400
              : (isHovered ? Color(0xff0058FF) : Colors.black),
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class HoverableSearchIcon extends StatefulWidget {
  const HoverableSearchIcon({super.key});

  @override
  _HoverableSearchIconState createState() => _HoverableSearchIconState();
}

class _HoverableSearchIconState extends State<HoverableSearchIcon> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: CustomImage.asset(
        'assets/images/my_business/search_collection.svg',
        width: 20,
        height: 20,
        fit: BoxFit.contain,
        color: _isHovered ? Colors.blue : null,
      ).toWidget(),
    );
  }
}