import 'package:flutter/material.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';

void main() {
  runApp(MaterialApp(home: ProjectDetailsForm(data: {})));
}

class ProjectDetailsForm extends StatefulWidget {
  const ProjectDetailsForm({super.key, required this.data});
  final Map<String, dynamic> data;

  @override
  State<ProjectDetailsForm> createState() => _ProjectDetailsFormState();
}

class _ProjectDetailsFormState extends State<ProjectDetailsForm> {
  int onboardingValue = 0;
  List currentSubIndustry = [];
  List<SubIndustry> organizationSize = [
    SubIndustry(
        title: "Small (1-10 employees)",
        subtitle: "Single location, owner-operated"),
    SubIndustry(
        title: "Medium (11-50 employees)",
        subtitle: "Growing business, some specialization"),
    SubIndustry(
        title: "Large (51-200 employees)",
        subtitle: "Multiple departments, formal processes"),
    SubIndustry(
        title: "Enterprise (200+ employees)",
        subtitle: "Complex hierarchy, multiple locations"),
  ];
  List<SubIndustry> locations = [
    SubIndustry(
      title: "Single Location",
    ),
    SubIndustry(
      title: "2-5 Locations (Same City)",
    ),
    SubIndustry(
      title: "6-20 Locations (Multiple Cities)",
    ),
    SubIndustry(
      title: "20+ Locations (National/International)",
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Row(
        children: [
          Expanded(
            child: SizedBox(),
          ),
          Expanded(child: Center(child: getCurrentMethod())),
          Expanded(
            child: SizedBox(),
          ),
        ],
      ),
    );
  }

  Widget getCurrentMethod() {
    switch (onboardingValue) {
      case 0:
        return formContainer(
            question: "What’s your Industry?",
            title: "Select Your Industry?",
            subtitle:
                "Helps us suggest industry-specific processes and requirements",
            list: industries);
      case 1:
        return formContainer(
            question: "Select Your Sub-Industry?",
            title: "Select Your Industry?",
            subtitle:
                "Helps us suggest industry-specific processes and requirements",
            list: currentSubIndustry);
      case 2:
        return formContainer(
            question: "Your Organisation?",
            title: "What’s Your Organization Size?",
            subtitle:
                "Understanding your scale helps determine solution complexity",
            list: organizationSize);
      case 3:
        return formContainer(
            question: "Your Organisation Locations?",
            title: "Your Operative Locations?",
            subtitle:
                "Understanding your scale helps determine solution complexity",
            list: locations);

      default:
        return Container();
    }
  }

  Widget formContainer(
      {String question = '',
      String title = '',
      String subtitle = '',
      List list = const []}) {
    return Container(
        margin: EdgeInsets.symmetric(vertical: AppSpacing.xl),
        child: Column(
          children: [
            Text(
              question,
              style: FontManager.getCustomStyle(
                  fontSize: FontManager.s22,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontWeight: FontWeight.w600),
            ),
            SizedBox(height: AppSpacing.md),
            progressContainer(
              count: 5,
            ),
            MouseRegion(
              cursor: onboardingValue > 0
                  ? SystemMouseCursors.click
                  : SystemMouseCursors.forbidden,
              child: GestureDetector(
                onTap: () {
                  if (onboardingValue > 0) {
                    onboardingValue--;
                  }
                  setState(() {});
                },
                child: Row(
                  children: [
                    Icon(
                      Icons.arrow_back,
                      size: 15,
                    ),
                    const SizedBox(
                      width: AppSpacing.xxs,
                    ),
                    Text(
                      "Back",
                      style: FontManager.getCustomStyle(
                          color: AppColors.black,
                          fontSize: FontManager.s12,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontWeight: FontWeight.w400),
                    )
                  ],
                ),
              ),
            ),
            SizedBox(height: AppSpacing.md),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  gradient: LinearGradient(
                      colors: [Color(0xff33B4F8), Color(0xff0082BC)])),
              padding: EdgeInsets.all(AppSpacing.sm),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: FontManager.getCustomStyle(
                              color: AppColors.white,
                              fontSize: FontManager.s18,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              fontWeight: FontWeight.w700),
                        ),
                        Text(
                          "(Mandatory)",
                          style: FontManager.getCustomStyle(
                              color: AppColors.white,
                              fontSize: FontManager.s12,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              fontWeight: FontWeight.w400),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Text(
                      subtitle,
                      style: FontManager.getCustomStyle(
                          color: AppColors.white,
                          fontSize: FontManager.s12,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontWeight: FontWeight.w400),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: AppSpacing.md),
            Expanded(
              child: ListView.builder(
                itemCount: list.length,
                shrinkWrap: true,
                physics: AlwaysScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  var e = list[index];
                  return MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () {
                        if (onboardingValue == 0) {
                          widget.data.addAll({"industry": e.title});
                          currentSubIndustry = e.subIndustries;
                        } else if (onboardingValue == 1) {
                          widget.data.addAll({"subIndustry": e.title});
                        } else if (onboardingValue == 2) {
                          widget.data.addAll({"organisation": e.title});
                        } else if (onboardingValue == 3) {
                          widget.data.addAll({"locations": e.title});
                        }
                        if (onboardingValue != 3) {
                          onboardingValue++;
                          setState(() {});
                        }
                        print(widget.data);
                      },
                      child: Container(
                        padding: EdgeInsets.all(AppSpacing.md),
                        margin: EdgeInsets.only(bottom: AppSpacing.xs),
                        decoration: BoxDecoration(
                          boxShadow: [
                            BoxShadow(
                                color: Color(0xff0000000f),
                                offset: Offset(0, 1),
                                blurRadius: 2)
                          ],
                          borderRadius: BorderRadius.circular(4),
                          border:
                              Border.all(color: Color(0xffECECEC), width: 0.5),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    e.title,
                                    style: FontManager.getCustomStyle(
                                        color: AppColors.black,
                                        fontSize: FontManager.s14,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        fontWeight: FontWeight.w600),
                                  ),
                                  e.subtitle != null
                                      ? Text(
                                          e.subtitle!,
                                          style: FontManager.getCustomStyle(
                                              color: AppColors.black,
                                              fontSize: FontManager.s10,
                                              fontFamily: FontManager
                                                  .fontFamilyTiemposText,
                                              fontWeight: FontWeight.w400),
                                        )
                                      : Container(),
                                ],
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 15,
                              color: Color(0xff707070),
                            )
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            )
          ],
        ));
  }

  Widget progressContainer({int count = 0}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        count,
        (index) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: AppSpacing.xs),
            decoration: BoxDecoration(
              color: index <= onboardingValue
                  ? Color(0xff0058FF)
                  : Color(0xffD0D0D0),
              borderRadius: BorderRadius.circular(20),
            ),
            height: 8,
            width: 40,
          );
        },
      ),
    );
  }
}

class SubIndustry {
  final String title;
  final String? subtitle;

  SubIndustry({required this.title, this.subtitle});
}

class Industry {
  final String title;
  final String? subtitle;
  final List<SubIndustry> subIndustries;

  Industry({required this.title, this.subtitle, required this.subIndustries});
}

final List<Industry> industries = [
  Industry(
    title: "E-commerce Platform",
    subtitle: "Online retail, marketplaces, and product sales",
    subIndustries: [
      SubIndustry(
          title: "Fashion & Apparel",
          subtitle: "Clothing, accessories, and footwear"),
      SubIndustry(
          title: "Electronics Store",
          subtitle: "Consumer electronics and gadgets"),
      SubIndustry(
          title: "Grocery Delivery",
          subtitle: "Online fresh food and grocery delivery"),
    ],
  ),
  Industry(
    title: "Human Resource Management System (HRMS)",
    subtitle: "Employee records, payroll, and performance management",
    subIndustries: [
      SubIndustry(
          title: "Recruitment Agencies",
          subtitle: "Talent sourcing and hiring processes"),
      SubIndustry(
          title: "Corporate HR",
          subtitle: "Internal HR departments of enterprises"),
      SubIndustry(
          title: "Payroll Services",
          subtitle: "Salary and compliance management"),
    ],
  ),
  Industry(
    title: "Customer Relationship Management (CRM)",
    subtitle: "Sales, customer interaction, and relationship tracking",
    subIndustries: [
      SubIndustry(
          title: "Real Estate CRM",
          subtitle: "Lead management and property tracking"),
      SubIndustry(
          title: "Automotive CRM",
          subtitle: "Dealership sales and customer service"),
      SubIndustry(
          title: "SaaS CRM", subtitle: "Software product customer engagement"),
    ],
  ),
  Industry(
    title: "Enterprise Resource Planning (ERP)",
    subtitle: "Business operations, finance, and resource planning",
    subIndustries: [
      SubIndustry(
          title: "Manufacturing ERP",
          subtitle: "Production and supply chain management"),
      SubIndustry(
          title: "Retail ERP",
          subtitle: "Inventory and point-of-sale integration"),
      SubIndustry(
          title: "Construction ERP",
          subtitle: "Project and equipment management"),
    ],
  ),
  Industry(
    title: "Learning Management System (LMS)",
    subtitle: "Online education and training platforms",
    subIndustries: [
      SubIndustry(
          title: "K-12 Education",
          subtitle: "School-based curriculum management"),
      SubIndustry(
          title: "Corporate Training",
          subtitle: "Employee learning and certifications"),
      SubIndustry(
          title: "University LMS",
          subtitle: "Higher education digital platforms"),
    ],
  ),
  Industry(
    title: "Content Management System (CMS)",
    subtitle: "Website content creation, publishing, and management",
    subIndustries: [
      SubIndustry(
          title: "Blog Platforms",
          subtitle: "Personal and professional blogging tools"),
      SubIndustry(
          title: "News & Media", subtitle: "Digital publishing and journalism"),
      SubIndustry(
          title: "Marketing Websites",
          subtitle: "Landing pages and product showcases"),
    ],
  ),
  Industry(
    title: "Inventory Management System",
    subtitle: "Stock, warehouse, and supply chain tracking",
    subIndustries: [
      SubIndustry(
          title: "Warehouse Management",
          subtitle: "Inventory control and distribution"),
      SubIndustry(
          title: "Retail Inventory",
          subtitle: "Store stock tracking and alerts"),
      SubIndustry(
          title: "Wholesale Inventory", subtitle: "Bulk product management"),
    ],
  ),
  Industry(
    title: "Accounting & Financial Management",
    subtitle: "Finance, expenses, payroll, and budgeting",
    subIndustries: [
      SubIndustry(
          title: "Small Business Accounting",
          subtitle: "Invoicing and tax management"),
      SubIndustry(
          title: "Enterprise Finance",
          subtitle: "Multi-entity financial planning"),
      SubIndustry(
          title: "Freelancer Finance",
          subtitle: "Self-employment income tracking"),
    ],
  ),
  Industry(
    title: "Project Management System",
    subtitle: "Task planning, timelines, and team collaboration",
    subIndustries: [
      SubIndustry(
          title: "Agile Teams",
          subtitle: "Scrum and Kanban-based project flow"),
      SubIndustry(
          title: "Construction Projects",
          subtitle: "On-site task scheduling and progress"),
      SubIndustry(
          title: "IT Services",
          subtitle: "Software development lifecycle tracking"),
    ],
  ),
  Industry(
    title: "Booking & Reservation System",
    subtitle: "Appointments, reservations, and ticketing",
    subIndustries: [
      SubIndustry(
          title: "Hotels & Hospitality",
          subtitle: "Room booking and guest management"),
      SubIndustry(
          title: "Event Ticketing", subtitle: "Concerts, shows, and seminars"),
      SubIndustry(
          title: "Healthcare Booking",
          subtitle: "Doctor appointments and follow-ups"),
    ],
  ),
  Industry(
    title: "Healthcare Management System",
    subtitle: "Medical practices, hospitals, clinics",
    subIndustries: [
      SubIndustry(
          title: "Cardiology Clinic",
          subtitle: "Heart and vascular system care"),
      SubIndustry(
          title: "Neurology Clinic",
          subtitle: "Disorders of the brain, spinal cord, and nervous system"),
      SubIndustry(
          title: "Dental Clinic", subtitle: "Oral healthcare and treatments"),
    ],
  ),
  Industry(
    title: "Logistics & Supply Chain Management",
    subtitle: "Shipping, warehousing, and fleet management",
    subIndustries: [
      SubIndustry(
          title: "Freight Management",
          subtitle: "Cargo tracking and route planning"),
      SubIndustry(
          title: "3PL Services", subtitle: "Third-party logistics operations"),
      SubIndustry(
          title: "E-commerce Fulfillment",
          subtitle: "Order packing and delivery management"),
    ],
  ),
  Industry(
    title: "Point of Sale (POS) System",
    subtitle: "Retail checkout, sales, and billing",
    subIndustries: [
      SubIndustry(title: "Retail POS", subtitle: "In-store sales processing"),
      SubIndustry(
          title: "Restaurant POS",
          subtitle: "Order management and table billing"),
      SubIndustry(
          title: "Mobile POS",
          subtitle: "On-the-go sales for vendors and kiosks"),
    ],
  ),
  Industry(
    title: "Workflow Management System",
    subtitle: "Business process automation and task coordination",
    subIndustries: [
      SubIndustry(
          title: "Legal Workflows",
          subtitle: "Case management and documentation"),
      SubIndustry(
          title: "IT Service Management",
          subtitle: "Issue tracking and escalations"),
      SubIndustry(
          title: "Procurement Workflows",
          subtitle: "Purchase request and approval flows"),
    ],
  ),
  Industry(
    title: "Document Management System",
    subtitle: "Digital file storage, access control, and compliance",
    subIndustries: [
      SubIndustry(
          title: "Law Firms",
          subtitle: "Legal document archiving and retrieval"),
      SubIndustry(
          title: "Healthcare DMS", subtitle: "Medical records and compliance"),
      SubIndustry(
          title: "Enterprise Content",
          subtitle: "Internal document collaboration"),
    ],
  ),
  Industry(
    title: "Customer Portal",
    subtitle: "Self-service access to support, orders, and data",
    subIndustries: [
      SubIndustry(
          title: "Banking Portals",
          subtitle: "Account info and transaction access"),
      SubIndustry(
          title: "Utility Services",
          subtitle: "Bill payments and service management"),
      SubIndustry(
          title: "E-commerce Portals", subtitle: "Order tracking and returns"),
    ],
  ),
  Industry(
    title: "Analytics & Reporting System",
    subtitle: "Business intelligence and data visualization",
    subIndustries: [
      SubIndustry(
          title: "Marketing Analytics",
          subtitle: "Campaign performance and ROI"),
      SubIndustry(
          title: "Financial Reporting", subtitle: "Budgeting and forecasts"),
      SubIndustry(
          title: "Operational Analytics",
          subtitle: "KPIs and process monitoring"),
    ],
  ),
  Industry(
    title: "Mobile Application",
    subtitle: "iOS and Android app development",
    subIndustries: [
      SubIndustry(
          title: "Consumer Apps",
          subtitle: "B2C utilities, lifestyle, and shopping"),
      SubIndustry(
          title: "Enterprise Apps", subtitle: "Internal productivity tools"),
      SubIndustry(
          title: "Gaming Apps",
          subtitle: "Casual and competitive mobile games"),
    ],
  ),
  Industry(
    title: "System Integration",
    subtitle: "Linking software, APIs, and databases",
    subIndustries: [
      SubIndustry(
          title: "ERP Integration",
          subtitle: "Connecting finance and operations systems"),
      SubIndustry(
          title: "CRM Integration",
          subtitle: "Sync customer data across platforms"),
      SubIndustry(
          title: "IoT Integration",
          subtitle: "Hardware and sensor data pipelines"),
    ],
  ),
  Industry(
    title: "Marketplace Platform",
    subtitle: "Multi-vendor product/service listing systems",
    subIndustries: [
      SubIndustry(
          title: "B2B Marketplace",
          subtitle: "Wholesale and business supply exchange"),
      SubIndustry(
          title: "Service Marketplace",
          subtitle: "Freelancers and on-demand workers"),
      SubIndustry(
          title: "Product Marketplace",
          subtitle: "Consumer goods and e-commerce"),
    ],
  ),
  Industry(
    title: "Collaboration Platform",
    subtitle: "Team communication, planning, and file sharing",
    subIndustries: [
      SubIndustry(
          title: "Remote Teams", subtitle: "Distributed work management"),
      SubIndustry(
          title: "Agile Product Teams",
          subtitle: "Sprint planning and documentation"),
      SubIndustry(
          title: "Design Collaboration",
          subtitle: "Asset reviews and creative workflows"),
    ],
  ),
  Industry(
    title: "Communication System",
    subtitle: "Real-time messaging, voice, and video",
    subIndustries: [
      SubIndustry(
          title: "Customer Support Chat",
          subtitle: "Live chat and ticketing systems"),
      SubIndustry(
          title: "Corporate Communication",
          subtitle: "Internal video and voice tools"),
      SubIndustry(
          title: "Telehealth Platforms",
          subtitle: "Remote medical consultation and communication"),
    ],
  ),
  Industry(
    title: "Other",
    subtitle: "Custom or unique software solutions",
    subIndustries: [
      SubIndustry(
          title: "IoT Applications",
          subtitle: "Smart device control and monitoring"),
      SubIndustry(
          title: "Blockchain Solutions",
          subtitle: "Decentralized apps and smart contracts"),
      SubIndustry(
          title: "Custom Enterprise Tools",
          subtitle: "Tailored business-specific software"),
    ],
  ),
];
