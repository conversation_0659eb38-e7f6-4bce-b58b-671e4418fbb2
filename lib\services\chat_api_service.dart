import 'dart:convert';
import 'dart:developer';
import 'dart:math' as math;
import 'package:dio/dio.dart';
import 'package:highlight/languages/http.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';
import 'package:nsl/services/auth_service.dart';
import 'package:nsl/utils/shared_preferences_helper.dart';
import '../config/environment.dart';
import '../services/dio_client.dart';
import '../utils/logger.dart';

class ChatApiService {
  final Dio _dio = DioClient().client;

  // Base URL
  late final String _baseUrl;
  late final String _nslBaseUrl;

  late final String _baseBRDURL;
  late final String _conversationURL;

  // API endpoints
  late final String _generalApiUrl;
  late final String _internetApiUrl;
  late final String _nslApiUrl;
  late final String _manageConversationsUrl;
  late final String _getChatHistoryUrl;
  late final String _newConversationUrl;
  late final String _conversationUrl;
  late final String _solutionSessionsUrl;
  late final String _solutionMessageUrl;
  late final String _solutionStatusUrl;
  late final String _modesUrl;
  late final String _modeSessionsUrl;
  late final String _modeChatUrl;
  late final String _nslChatUrl;
  late final String _startStreamUrl;
  late final String _continueStreamUrl;

  // Singleton instance
  static final ChatApiService _instance = ChatApiService._internal();

  // Factory constructor
  factory ChatApiService() => _instance;

  // Internal constructor
  ChatApiService._internal() {
    // Initialize base URL from environment
    _baseUrl = Environment.chatApiBaseUrl;
    _baseBRDURL = Environment.brdBaseUrl;
    _conversationURL = Environment.conversationUrl;
    Logger.info('Chat API base URL: $_baseUrl');

    // Initialize API endpoints
    _generalApiUrl = '$_baseUrl/ask/general';
    _internetApiUrl = '$_baseUrl/ask/internet';
    _nslApiUrl = '$_baseUrl/ask/nsl';
    _manageConversationsUrl = '$_baseUrl/manage_conversations';
    _getChatHistoryUrl = '$_baseUrl/get_chat_history';
    _newConversationUrl = '$_conversationURL/conversation/new';
    _conversationUrl = '$_conversationURL/conversation';

    // _solutionSessionsUrl = '$_baseBRDURL/api/enhanced-brd/start';
    // _solutionMessageUrl = '$_baseBRDURL/api/enhanced-brd/continue';
    // _solutionStatusUrl = '$_baseBRDURL/api/enhanced-brd/status/';

    _solutionSessionsUrl = '$_baseBRDURL/api/v1/brd/start';
    _solutionMessageUrl = '$_baseBRDURL/api/v1/brd/continue';
    _solutionStatusUrl = '$_baseBRDURL/v1/brd/{conversion_id}/state';

    _modesUrl = '$_baseUrl/modes';
    _modeSessionsUrl = '$_baseUrl/sessions';
    _modeChatUrl = '$_baseUrl/chat';
    _nslChatUrl = '${Environment.nslBaseUrl}/api/v1/chat';

    // _startStreamUrl = '$_baseBRDURL/api/v3/brd/start/stream';
    _startStreamUrl = '$_baseBRDURL/api/v1/brd/start-stream';
    // _continueStreamUrl = '$_baseBRDURL/api/v3/brd/continue/stream';
    _continueStreamUrl = '$_baseBRDURL/api/v1/brd/continue-stream';
    // _nslChatUrl = '$_nslBaseUrl/api/v1/chat';
  }

  // Create a new conversation
  Future<Map<String, dynamic>> createConversation(
      String userId, String title) async {
    try {
      Logger.info(
          'Creating new conversation for user $userId with title: $title');

      // Prepare request payload
      final payload = {'action': 'create', 'user_id': userId, 'title': title};

      // Make API call
      final response = await _dio.post(
        _manageConversationsUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        // Extract conversation ID from response
        final conversationId = response.data['conversation_id'];

        if (conversationId != null) {
          Logger.info('Created new conversation with ID: $conversationId');

          return {
            'success': true,
            'data': response.data,
            'conversation_id': conversationId.toString(),
          };
        } else {
          Logger.error(
              'Create conversation API did not return a conversation_id');
          return {
            'success': false,
            'message': 'No conversation ID returned',
          };
        }
      } else {
        Logger.error(
            'Create conversation API failed with status: ${response.statusCode}');
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error creating conversation: $e');
      return {
        'success': false,
        'message': 'Failed to create conversation: $e',
      };
    }
  }

  // Send a general question to the API
  Future<Map<String, dynamic>> sendGeneralQuestion(
      String question, String conversationId, String userId) async {
    try {
      Logger.info('Sending general question to API: $question');

      // Prepare request payload
      final payload = {
        'question': question,
        'user_id': userId,
        'conversation_id': conversationId,
        'include_reasoning': true
      };

      // Make API call
      final response = await _dio.post(
        _generalApiUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('General API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error sending general question: $e');
      return {
        'success': false,
        'message': 'Failed to send question: $e',
      };
    }
  }

  // Send an internet question to the API
  Future<Map<String, dynamic>> sendInternetQuestion(
      String question, String conversationId, String userId) async {
    try {
      Logger.info('Sending internet question to API: $question');

      // Prepare request payload with conversation ID
      final payload = {
        'question': question,
        'user_id': userId,
        'conversation_id': conversationId,
        'include_reasoning': true
      };

      // Make API call
      final response = await _dio.post(
        _internetApiUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('Internet API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error sending internet question: $e');
      return {
        'success': false,
        'message': 'Failed to send internet question: $e',
      };
    }
  }

  // Send an NSL question to the API
  Future<Map<String, dynamic>> sendNslQuestion(
      String question, String conversationId, String userId) async {
    try {
      Logger.info('Sending NSL question to API: $question');

      // Prepare request payload with conversation ID
      final payload = {
        'question': question,
        'user_id': userId,
        'conversation_id': conversationId,
        'include_reasoning': true
      };

      // Make API call
      final response = await _dio.post(
        _nslApiUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('NSL API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error sending NSL question: $e');
      return {
        'success': false,
        'message': 'Failed to send NSL question: $e',
      };
    }
  }

  // Fetch chat history list using new mode sessions API
  Future<Map<String, dynamic>> fetchChatHistory(String userId) async {
    try {
      Logger.info('Fetching chat history for user: $userId');

      // Make API call to new sessions endpoint
      final response = await _dio.get(
        '$_modeSessionsUrl/$userId',
        options: Options(
          headers: {
            'accept': 'application/json',
          },
          validateStatus: (status) => true, // Accept all status codes
        ),
      );

      Logger.info('Chat history API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        // Extract error message from API response
        String errorMessage = 'Failed to fetch chat history.';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            errorMessage = response.data['detail'] ??
                response.data['message'] ??
                response.data['error'] ??
                errorMessage;
          } else if (response.data is String) {
            errorMessage = response.data;
          }
        }

        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Error fetching chat history: $e');
      return {
        'success': false,
        'message': 'Something went wrong. Please try again',
      };
    }
  }

  // Fetch chat history for a specific session using new mode chat history API
  Future<Map<String, dynamic>> fetchConversationHistory(
      String sessionId, String userId) async {
    try {
      Logger.info('Fetching chat history for session: $sessionId');

      // Make API call to new chat history endpoint
      final response = await _dio.get(
        '$_modeChatUrl/$sessionId/history',
        options: Options(
          headers: {
            'accept': 'application/json',
          },
          validateStatus: (status) => true, // Accept all status codes
        ),
      );

      Logger.info(
          'Get chat history API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
          'session_id': sessionId,
        };
      } else {
        // Extract error message from API response
        String errorMessage = 'Failed to fetch conversation history.';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            errorMessage = response.data['detail'] ??
                response.data['message'] ??
                response.data['error'] ??
                errorMessage;
          } else if (response.data is String) {
            errorMessage = response.data;
          }
        }

        return {
          'success': false,
          'message': errorMessage,
          'session_id': sessionId,
        };
      }
    } catch (e) {
      Logger.error('Error fetching conversation history: $e');
      return {
        'success': false,
        'message': 'Something went wrong. Please try again',
        'session_id': sessionId,
      };
    }
  }

  // Create a new conversation session
  Future<Map<String, dynamic>> createNewConversationSession() async {
    try {
      Logger.info('Creating new conversation session');

      // Make API call with empty body
      final response = await _dio.post(
        _newConversationUrl,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create new conversation session API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        // The response directly contains session_id and message
        return {
          'success': true,
          'session_id': response.data['session_id'],
          'message': response.data['message'],
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error creating new conversation session: $e');
      return {
        'success': false,
        'message': 'Failed to create new conversation session: $e',
      };
    }
  }

  // Send user input to conversation API
  Future<Map<String, dynamic>> sendConversationInput(
      String sessionId, String userInput,
      {FileUploadOcrResponse? fileData}) async {
    try {
      Logger.info('Sending user input to conversation API');

      // Prepare request payload
      final Map<String, dynamic> payload = {
        'session_id': sessionId,
        'user_input': userInput,
      };

      // Add file data if available
      // if (fileData != null) {
      //   payload['file_data'] = fileData;
      //   // Logger.info(
      //   //     'Including file data in conversation API request: ${fileData['fileName']}');
      // }

      // Make API call
      final response = await _dio.post(
        _conversationUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('Conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error sending user input to conversation API: $e');
      return {
        'success': false,
        'message': 'Failed to send user input to conversation API: $e',
      };
    }
  }

  // Create a new solution session for the first message (streaming)
  Stream<Map<String, dynamic>> createSolutionSession(String tenantId,
      String projectId, String initialInput, String userId) async* {
    try {
      Logger.info('Creating new solution session for tenant: $tenantId');

      // Prepare request payload
      final Map<String, dynamic> payload = {
        'tenant_id': tenantId,
        'project_id': projectId,
        'initial_input': initialInput,
        'user_id': userId,
        // 'industry':,
        // 'company_size':,
      };

      // Make streaming API call
      final response = await _dio.post(
        _solutionSessionsUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
          },
          //responseType: ResponseType.stream,
        ),
      );

      Logger.info(
          'Solution session API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        // Handle streaming response
        final stream = response.data.stream;
        String buffer = '';

        await for (final chunk
            in stream.cast<List<int>>().transform(utf8.decoder)) {
          buffer += chunk;

          // Process complete lines
          while (buffer.contains('\n')) {
            final lineEnd = buffer.indexOf('\n');
            final line = buffer.substring(0, lineEnd).trim();
            buffer = buffer.substring(lineEnd + 1);

            if (line.isEmpty) continue;

            // Parse Server-Sent Events format
            if (line.startsWith('event: ')) {
              final eventType = line.substring(7);
              continue;
            }

            if (line.startsWith('data: ')) {
              final dataStr = line.substring(6);
              try {
                final data = jsonDecode(dataStr) as Map<String, dynamic>;
                final eventType = data['event_type'] as String?;

                Logger.info('Received streaming event: $eventType');

                // Yield the parsed event data
                yield {
                  'success': true,
                  'event_type': eventType,
                  'data': data,
                  'timestamp': data['timestamp'],
                };

                // Special handling for question events
                if (eventType == 'question') {
                  Logger.info(
                      'Question event received: ${data['data']['question']}');
                }

                // Handle completion event
                if (eventType == 'completion') {
                  Logger.info('Solution session completed successfully');
                  break;
                }
              } catch (e) {
                Logger.error('Error parsing streaming data: $e');
                yield {
                  'success': false,
                  'message': 'Error parsing streaming data: $e',
                };
              }
            }
          }
        }
      } else {
        yield {
          'success': false,
          'message': 'HTTP ${response.statusCode}: ${response.statusMessage}',
        };
      }
    } catch (e) {
      Logger.error('Error creating solution session: $e');
      yield {
        'success': false,
        'message': 'Failed to create solution session: $e',
      };
    }
  }

  Stream<Map<String, dynamic>> createSolutionMessage(String? sessionId,
      String userResponse, String? nodeId, String? userId) async* {
    try {
      Logger.info('Creating solution message for session: $sessionId');

      // Prepare request payload
      final Map<String, dynamic> payload = {
        'session_id': sessionId,
        'user_response': userResponse,
        'conversation_id': nodeId,
        'user_id': userId,
      };

      // Make streaming API call
      final response = await _dio.post(
        _solutionMessageUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
          },
          responseType: ResponseType.stream,
        ),
      );

      Logger.info(
          'Solution message API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        // Handle streaming response
        final stream = response.data.stream;
        String buffer = '';

        await for (final chunk
            in stream.cast<List<int>>().transform(utf8.decoder)) {
          buffer += chunk;

          // Process complete lines
          while (buffer.contains('\n')) {
            final lineEnd = buffer.indexOf('\n');
            final line = buffer.substring(0, lineEnd).trim();
            buffer = buffer.substring(lineEnd + 1);

            if (line.isEmpty) continue;

            // Parse Server-Sent Events format
            if (line.startsWith('event: ')) {
              final eventType = line.substring(7);
              continue;
            }

            if (line.startsWith('data: ')) {
              final dataStr = line.substring(6);
              try {
                final data = jsonDecode(dataStr) as Map<String, dynamic>;
                final eventType = data['event_type'] as String?;

                Logger.info('Received streaming event: $eventType');

                // Yield the parsed event data
                yield {
                  'success': true,
                  'event_type': eventType,
                  'data': data,
                  'timestamp': data['timestamp'],
                };

                // Special handling for question events
                if (eventType == 'question') {
                  Logger.info(
                      'Question event received: ${data['data']['question']}');
                }

                // Handle completion event
                if (eventType == 'completion') {
                  Logger.info('Solution message completed successfully');
                  break;
                }
              } catch (e) {
                Logger.error('Error parsing streaming data: $e');
                yield {
                  'success': false,
                  'message': 'Error parsing streaming data: $e',
                };
              }
            }
          }
        }
      } else {
        yield {
          'success': false,
          'message': 'HTTP ${response.statusCode}: ${response.statusMessage}',
        };
      }
    } catch (e) {
      Logger.error('Error creating solution message: $e');
      yield {
        'success': false,
        'message': 'Failed to create solution message: $e',
      };
    }
  }

  Future<Map<String, dynamic>> getSolutionStatus(
    String? sessionId,
    String? userId,
  ) async {
    try {
      // Prepare request payload
      final Map<String, dynamic> payload = {};

      // Make API call
      final response = await _dio.get(
        "$_solutionStatusUrl$sessionId?user_id=$userId",
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Solution session API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error creating solution session: $e');
      return {
        'success': false,
        'message': 'Failed to create solution status: $e',
      };
    }
  }

  // Fetch available modes
  Future<Map<String, dynamic>> fetchModes() async {
    try {
      Logger.info('Fetching available modes');

      // Make API call
      final response = await _dio.get(
        _modesUrl,
        options: Options(
          headers: {
            'accept': 'application/json',
          },
        ),
      );

      Logger.info('Modes API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error fetching modes: $e');
      return {
        'success': false,
        'message': 'Failed to fetch modes: $e',
      };
    }
  }

  // Create a new mode session
  Future<Map<String, dynamic>> createModeSession(
      String userId, String title) async {
    try {
      Logger.info(
          'Creating new mode session for user: $userId with title: $title');

      // Make API call with query parameters
      final response = await _dio.post(
        '$_modeSessionsUrl?user_id=$userId&title=${Uri.encodeComponent(title)}',
        options: Options(
          headers: {
            'accept': 'application/json',
          },
          validateStatus: (status) => true, // Accept all status codes
        ),
      );

      Logger.info('Mode session API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
          'session_id': response.data['session_id'],
        };
      } else {
        // Extract error message from API response
        String errorMessage = 'Something went wrong. Please try again.';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            // Try to get 'detail' field first, then 'message', then 'error'
            errorMessage = response.data['detail'] ??
                response.data['message'] ??
                response.data['error'] ??
                errorMessage;
          } else if (response.data is String) {
            errorMessage = response.data;
          }
        }

        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Error creating mode session: $e');
      return {
        'success': false,
        'message': 'Something went wrong. Please try again',
      };
    }
  }

  // Send a message to mode chat
  Future<Map<String, dynamic>> sendModeChat(
      String message, String sessionId, String mode,
      {bool forceModeSwitch = false}) async {
    try {
      Logger.info('Sending mode chat message with mode: $mode');

      // Prepare request payload
      final payload = {
        'message': message,
        'session_id': sessionId,
        // 'mode': mode,
        'mode': 'nsl_expert',
        'force_mode_switch': forceModeSwitch,
      };

      // Make API call
      final response = await _dio.post(
        _modeChatUrl,
        data: payload,
        options: Options(
          headers: {
            'accept': 'application/json',
            'Content-Type': 'application/json',
          },
          validateStatus: (status) => true, // Accept all status codes
        ),
      );

      Logger.info('Mode chat API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        // Extract error message from API response
        String errorMessage = 'Something went wrong. Please try again.';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            // Try to get 'detail' field first, then 'message', then 'error'
            errorMessage = response.data['detail'] ??
                response.data['message'] ??
                response.data['error'] ??
                errorMessage;
          } else if (response.data is String) {
            errorMessage = response.data;
          }
        }

        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Error sending mode chat: $e');
      return {
        'success': false,
        'message': 'Something went wrong. Please try again',
      };
    }
  }

  Future<Map<String, dynamic>> startSolutionSession(String tenantId,
      String projectId, String initialInput, String userId) async {
    // return {
    //   'success': true,
    //   'data': {
    //     "conversation_id": "aaa159ea-ee79-4951-8aa9-f77204bc2732",
    //     "current_phase": "organization",
    //     "ai_response":
    //         "<thinking>\n1. Domain Analysis: E-commerce - digital retail operations requiring customer-facing storefront, inventory, orders, payments\n2. Solution Scope: Enterprise platform with multiple integrated modules\n3. User Context: Customers (buyers), admins, inventory managers, customer service, fulfillment staff\n4. Module Identification: Product catalog, shopping cart, order management, customer accounts, inventory, payments\n5. Compliance: PCI-DSS for payments, GDPR/CCPA for customer data, ISO 27001 for security\n6. UI Format: Early conversation - SELECTION_OPTIONS to establish core requirements\n7. Next Steps: Need to identify primary functional areas to prioritize detailed specification\n</thinking>\n**OUTPUT 1:**\n**SOLUTION PROGRESS:** Initiating comprehensive e-commerce solution specification\n**SOLUTION UNDERSTANDING:** \nCreating a complete digital retail platform to manage product sales, customer interactions, and business operations. The solution will handle the entire purchase lifecycle from browsing to fulfillment while ensuring secure transactions and efficient inventory management.\n**SELECTION_OPTIONS:**\n□ Core Commerce: Specify product catalog, shopping cart, and checkout workflows\n□ Customer Experience: Define account management, wishlist, and review systems\n□ Operations Management: Configure inventory, fulfillment, and return processes\n□ Business Intelligence: Establish sales analytics, customer insights, and performance tracking\nWhich functional area should we specify first to align with your immediate priorities? ",
    //     "ai_suggestions": [
    //       "Which organizational structure best matches your operational needs?"
    //     ],
    //     "progress": {
    //       "overall_completion": 0.28,
    //       "phase_completion": {
    //         "foundation": 0.85,
    //         "organization": 0.55,
    //         "data": 0.0,
    //         "functions": 0.0,
    //         "processes": 0.0
    //       },
    //       "component_completion": {
    //         "foundation": {
    //           "user_intent_category":
    //               "Information about solution type, intent, category to be gathered in future conversations",
    //           "solution_scope_and_scale":
    //               "Solution Scope: Multi-module enterprise platform covering sales, inventory, customers, orders\n3",
    //           "industry_and_solution_type":
    //               "Domain Analysis: E-commerce - digital retail transactions, customer management, inventory, payments\n2",
    //           "target_users_and_context":
    //               "User Context: Administrators, customers, inventory managers, support staff\n4",
    //           "outcome_expectations":
    //               "Information about outcomes, benefits, success to be gathered in future conversations",
    //           "function_coverage_stats":
    //               "UI Format: Early conversation - SELECTION_OPTIONS to establish core requirements\n7. Strategy: Start with core module selection to establish foundation\n</thinking>\n\n**OUTPUT 1: IMMEDIATE RESPONSE**\n\n**SOLUTION PROGRESS**\nStarting your e-commerce solution specification with core business functions!\n\n**SOLUTION UNDERSTANDING**\nCreating a comprehensive e-commerce solution to manage online sales, product catalogs, customer interactions, and order fulfillment",
    //           "process_and_entity_summary":
    //               "Compliance: PCI-DSS for payments, GDPR/CCPA for customer data, ISO 27001\n6",
    //           "organizational_scope_metrics":
    //               "Solution Scope: Multi-module enterprise platform covering sales, inventory, customers, orders\n3",
    //           "technical_complexity_assessment":
    //               "Information about technical, complexity, integration to be gathered in future conversations",
    //           "implementation_readiness_score":
    //               "Information about readiness, implementation, timeline to be gathered in future conversations",
    //           "session_tracking_info":
    //               "Foundation phase conversation - 2025-07-02T15:19:16.721046",
    //           "information_source_quality": "High - Direct stakeholder input",
    //           "stakeholder_engagement_data":
    //               "Information about stakeholders, engagement, participation to be gathered in future conversations",
    //           "assumptions_and_gaps":
    //               "Information about assumptions, gaps, missing to be gathered in future conversations",
    //           "confidence_and_validation":
    //               "Medium - Requires validation in subsequent phases",
    //           "summary":
    //               "Foundation phase - 9 components captured covering business scope, stakeholders, and success metrics"
    //         },
    //         "organization": {
    //           "organizational_levels":
    //               "User Input Analysis: User confirmed focus on organization structure - need to detail roles and responsibilities\n3. UI Format: ESTABLISHED + SELECTION_OPTIONS for role structure definition\n7",
    //           "decision_authority_matrix":
    //               "Section Focus: Organization section development with role matrix and approval workflows\n5. Next Steps: Define specific role permissions and approval chains\n</thinking>\n\n**OUTPUT 1: CONVERSATIONAL RESPONSE**\n\n**SOLUTION PROGRESS**\nExcellent! Let's establish your organizational structure and role-based access controls",
    //           "information_flow_structure":
    //               "Information about information flow, communication, channels to be gathered in future conversations",
    //           "escalation_and_exception_handling":
    //               "**SOLUTION UNDERSTANDING**\nWe'll define a comprehensive role hierarchy with specific function assignments and approval workflows while ensuring compliance with PCI-DSS for payment handling and GDPR/CCPA for customer data management",
    //           "cross_functional_authority":
    //               "Information about cross-functional, coordination, authority to be gathered in future conversations",
    //           "role_inventory_and_hierarchy":
    //               "User Input Analysis: User confirmed focus on organization structure - need to detail roles and responsibilities\n3. **SOLUTION UNDERSTANDING**\nWe'll define a comprehensive role hierarchy with specific function assignments and approval workflows while ensuring compliance with PCI-DSS for payment handling and GDPR/CCPA for customer data management",
    //           "individual_role_functions":
    //               "User Input Analysis: User confirmed focus on organization structure - need to detail roles and responsibilities\n3",
    //           "individual_role_metrics":
    //               "Module Recommendations: Role management, access control, and performance tracking modules\n6",
    //           "role_permissions_and_access":
    //               "Module Recommendations: Role management, access control, and performance tracking modules\n6. Next Steps: Define specific role permissions and approval chains\n</thinking>\n\n**OUTPUT 1: CONVERSATIONAL RESPONSE**\n\n**SOLUTION PROGRESS**\nExcellent! Let's establish your organizational structure and role-based access controls",
    //           "role_coordination_patterns":
    //               "Information about coordination, collaboration, patterns to be gathered in future conversations",
    //           "departmental_organization":
    //               "Progress Analysis: Organization section needs development with 15% completion. User Input Analysis: User confirmed focus on organization structure - need to detail roles and responsibilities\n3",
    //           "departmental_role_groupings":
    //               "User Input Analysis: User confirmed focus on organization structure - need to detail roles and responsibilities\n3. UI Format: ESTABLISHED + SELECTION_OPTIONS for role structure definition\n7",
    //           "departmental_performance_metrics":
    //               "Module Recommendations: Role management, access control, and performance tracking modules\n6",
    //           "interdepartmental_coordination":
    //               "Information about interdepartmental, coordination, collaboration to be gathered in future conversations",
    //           "departmental_resource_allocation":
    //               "Module Recommendations: Role management, access control, and performance tracking modules\n6. **SOLUTION UNDERSTANDING**\nWe'll define a comprehensive role hierarchy with specific function assignments and approval workflows while ensuring compliance with PCI-DSS for payment handling and GDPR/CCPA for customer data management",
    //           "performance_measurement_framework":
    //               "Information about performance framework, measurement, approach to be gathered in future conversations",
    //           "performance_review_cycles":
    //               "Information about review cycles, evaluation, schedules to be gathered in future conversations",
    //           "performance_data_collection":
    //               "Information about data collection, methods, sources to be gathered in future conversations",
    //           "performance_reporting_structure":
    //               "User Input Analysis: User confirmed focus on organization structure - need to detail roles and responsibilities\n3. UI Format: ESTABLISHED + SELECTION_OPTIONS for role structure definition\n7",
    //           "performance_improvement_mechanisms":
    //               "Information about improvement, feedback, mechanisms to be gathered in future conversations",
    //           "summary":
    //               "Organization phase - 12 components captured covering roles, hierarchy, and performance management"
    //         },
    //         "data": {"summary": "Data phase - No data captured yet"},
    //         "functions": {"summary": "Functions phase - No data captured yet"},
    //         "processes": {"summary": "Processes phase - No data captured yet"}
    //       }
    //     },
    //     "ready_for_generation": false,
    //     "complete_json": {}
    //   },
    // };
    try {
      // Logger.info('Sending mode chat message with mode: $mode');
      String? modelKey = await SharedPreferencesHelper.getSelectedModelApiKey();
      // Prepare request payload
      final Map<String, dynamic> payload = {
        'tenant': tenantId,
        'project': initialInput,
        'initial_input': initialInput,
        'user_id': userId,
        'preferred_model': modelKey ?? "claude"
        // 'industry':,
        // 'company_size':,
      };

      // Make API call
      final response = await _dio.post(
        _solutionSessionsUrl,
        data: payload,
        options: Options(
          headers: {
            'accept': 'application/json',
            'Content-Type': 'application/json',
            // 'X-Model-Provider' :modelKey??"claude"
          },
          validateStatus: (status) => true, // Accept all status codes
        ),
      );

      Logger.info('Mode chat API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        // Extract error message from API response
        String errorMessage = 'Something went wrong. Please try again.';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            // Try to get 'detail' field first, then 'message', then 'error'
            errorMessage = response.data['detail'] ??
                response.data['message'] ??
                response.data['error'] ??
                errorMessage;
          } else if (response.data is String) {
            errorMessage = response.data;
          }
        }

        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Error sending mode chat: $e');
      return {
        'success': false,
        'message': 'Something went wrong. Please try again',
      };
    }
  }

  Future<Map<String, dynamic>> continueSolutionSession(
      String? currentSessionId, String text) async {
    try {
      // Logger.info('Sending mode chat message with mode: $mode');
      String? modelKey = await SharedPreferencesHelper.getSelectedModelApiKey();
      // Prepare request payload
      final Map<String, dynamic> payload = {
        'conversation_id': currentSessionId,
        'user_input': text,
        'preferred_model': modelKey ?? "claude"
        // 'industry':,
        // 'company_size':,
      };

      // Make API call
      final response = await _dio.post(
        _solutionMessageUrl,
        data: payload,
        options: Options(
          headers: {
            'accept': 'application/json',
            'Content-Type': 'application/json',
            // 'X-Model-Provider' :modelKey??"claude"
          },
          validateStatus: (status) => true, // Accept all status codes
        ),
      );

      Logger.info('Mode chat API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        // Extract error message from API response
        String errorMessage = 'Something went wrong. Please try again.';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            // Try to get 'detail' field first, then 'message', then 'error'
            errorMessage = response.data['detail'] ??
                response.data['message'] ??
                response.data['error'] ??
                errorMessage;
          } else if (response.data is String) {
            errorMessage = response.data;
          }
        }

        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Error sending mode chat: $e');
      return {
        'success': false,
        'message': 'Something went wrong. Please try again',
      };
    }
  }

  // Create a new NSL conversation session (first message)
  Future<Map<String, dynamic>> createNewNslConversationSession(
      String userId, String message,
      {String mode = "nsl_expert"}) async {
    try {
      Logger.info('Creating new NSL conversation session for user: $userId');

      // Prepare request payload without session_id for new conversation
      final payload = {
        'user_id': userId,
        'message': message,
        'mode': mode,
      };

      // Make API call
      final response = await _dio.post(
        _nslChatUrl,
        data: payload,
        options: Options(
          headers: {
            'accept': 'application/json',
            'Content-Type': 'application/json',
          },
          validateStatus: (status) => true, // Accept all status codes
        ),
      );

      Logger.info(
          'NSL conversation session API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
          'session_id': response.data['session_id'],
        };
      } else {
        // Extract error message from API response
        String errorMessage = 'Something went wrong. Please try again.';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            errorMessage = response.data['detail'] ??
                response.data['message'] ??
                response.data['error'] ??
                errorMessage;
          } else if (response.data is String) {
            errorMessage = response.data;
          }
        }

        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Error creating NSL conversation session: $e');
      return {
        'success': false,
        'message': 'Something went wrong. Please try again',
      };
    }
  }

  // Send message to existing NSL conversation session
  Future<Map<String, dynamic>> sendNslConversationMessage(
      String userId, String message, String sessionId,
      {String mode = "nsl_expert"}) async {
    try {
      Logger.info('Sending NSL conversation message to session: $sessionId');

      // Prepare request payload with session_id for continuing conversation
      final payload = {
        'user_id': userId,
        'message': message,
        'mode': mode,
        'session_id': sessionId,
      };

      // Make API call
      final response = await _dio.post(
        _nslChatUrl,
        data: payload,
        options: Options(
          headers: {
            'accept': 'application/json',
            'Content-Type': 'application/json',
          },
          validateStatus: (status) => true, // Accept all status codes
        ),
      );

      Logger.info(
          'NSL conversation message API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        // Extract error message from API response
        String errorMessage = 'Something went wrong. Please try again.';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            errorMessage = response.data['detail'] ??
                response.data['message'] ??
                response.data['error'] ??
                errorMessage;
          } else if (response.data is String) {
            errorMessage = response.data;
          }
        }

        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Error sending NSL conversation message: $e');
      return {
        'success': false,
        'message': 'Something went wrong. Please try again',
      };
    }
  }

  // NEW API METHODS FOR NSL CONVERSATIONS

  // Fetch all conversations for a specific user using new API
  Future<Map<String, dynamic>> fetchAllConversations(String userId) async {
    try {
      Logger.info('Fetching all conversations for user: $userId');

      // Make API call to new conversations endpoint using proper base URL
      final response = await _dio.get(
        'http://10.26.1.11:8605/api/v1/conversations?user_id=$userId',
        options: Options(
          headers: {
            'accept': 'application/json',
          },
          validateStatus: (status) => true, // Accept all status codes
        ),
      );

      Logger.info(
          'All conversations API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        // Extract error message from API response
        String errorMessage = 'Failed to fetch conversations.';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            errorMessage = response.data['detail'] ??
                response.data['message'] ??
                response.data['error'] ??
                errorMessage;
          } else if (response.data is String) {
            errorMessage = response.data;
          }
        }

        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Error fetching all conversations: $e');
      return {
        'success': false,
        'message': 'Something went wrong. Please try again',
      };
    }
  }

  // Fetch conversation messages using new API with flexible filtering and pagination
  Future<Map<String, dynamic>> fetchConversationMessages(
      String sessionId, String userId,
      {Map<String, dynamic>? options}) async {
    try {
      Logger.info('Fetching conversation messages for session: $sessionId');

      // Build query parameters
      Map<String, String> queryParams = {
        'session_id': sessionId,
        'user_id': userId,
      };

      // Add optional parameters if provided
      if (options != null) {
        if (options['limit'] != null) {
          queryParams['limit'] = options['limit'].toString();
        }
        if (options['offset'] != null) {
          queryParams['offset'] = options['offset'].toString();
        }
        if (options['from_date'] != null) {
          queryParams['from_date'] = options['from_date'].toString();
        }
        if (options['to_date'] != null) {
          queryParams['to_date'] = options['to_date'].toString();
        }
        if (options['role'] != null) {
          queryParams['role'] = options['role'].toString();
        }
      }

      // Build URL with query parameters using proper base URL
      final uri = Uri.parse('http://10.26.1.11:8605/api/v1/messages')
          .replace(queryParameters: queryParams);

      // Make API call to new messages endpoint
      final response = await _dio.get(
        uri.toString(),
        options: Options(
          headers: {
            'accept': 'application/json',
          },
          validateStatus: (status) => true, // Accept all status codes
        ),
      );

      Logger.info(
          'Conversation messages API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
          'session_id': sessionId,
        };
      } else {
        // Extract error message from API response
        String errorMessage = 'Failed to fetch conversation messages.';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            errorMessage = response.data['detail'] ??
                response.data['message'] ??
                response.data['error'] ??
                errorMessage;
          } else if (response.data is String) {
            errorMessage = response.data;
          }
        }

        return {
          'success': false,
          'message': errorMessage,
          'session_id': sessionId,
        };
      }
    } catch (e) {
      Logger.error('Error fetching conversation messages: $e');
      return {
        'success': false,
        'message': 'Something went wrong. Please try again',
        'session_id': sessionId,
      };
    }
  }

  // Delete a conversation
  Future<Map<String, dynamic>> deleteConversation(
      String sessionId, String userId) async {
    try {
      Logger.info('Deleting conversation for session: $sessionId');

      // Prepare request payload
      final payload = {
        'session_id': sessionId,
        'user_id': userId,
      };

      // Make API call to delete conversation endpoint using proper base URL
      final response = await _dio.delete(
        'http://10.26.1.11:8605/api/v1/conversations',
        data: payload,
        options: Options(
          headers: {
            'accept': 'application/json',
            'Content-Type': 'application/json',
          },
          validateStatus: (status) => true, // Accept all status codes
        ),
      );

      Logger.info(
          'Delete conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
          'session_id': sessionId,
        };
      } else {
        // Extract error message from API response
        String errorMessage = 'Failed to delete conversation.';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            errorMessage = response.data['detail'] ??
                response.data['message'] ??
                response.data['error'] ??
                errorMessage;
          } else if (response.data is String) {
            errorMessage = response.data;
          }
        }

        return {
          'success': false,
          'message': errorMessage,
          'session_id': sessionId,
        };
      }
    } catch (e) {
      Logger.error('Error deleting conversation: $e');
      return {
        'success': false,
        'message': 'Something went wrong. Please try again',
        'session_id': sessionId,
      };
    }
  }

  // Fetch chat history list using new mode sessions API
  Future<Map<String, dynamic>> fetchCreateChatHistory(String tenanId) async {
    try {
      Logger.info('Fetching create chat history for tenant Id: $tenanId');

      // Make API call to new sessions endpoint
      final response = await _dio.get(
        '$_baseBRDURL/api/v1/tenants/$tenanId/projects',
        options: Options(
          headers: {
            'accept': 'application/json',
          },
          validateStatus: (status) => true, // Accept all status codes
        ),
      );

      Logger.info('Chat history API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        // Extract error message from API response
        String errorMessage = 'Failed to fetch chat history.';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            errorMessage = response.data['detail'] ??
                response.data['message'] ??
                response.data['error'] ??
                errorMessage;
          } else if (response.data is String) {
            errorMessage = response.data;
          }
        }

        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Error fetching chat history: $e');
      return {
        'success': false,
        'message': 'Something went wrong. Please try again',
      };
    }
  }

  Future<Map<String, dynamic>> fetchConversationFromNewAPI(
      String projectId) async {
    try {
      final dio = Dio();

      final url =
          'http://10.26.1.52:8200/api/v1/projects/$projectId/conversations';

      final response = await dio.get(url);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to fetch: ${response.statusCode}',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': 'Dio error: ${e.message}',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Unexpected error: $e',
      };
    }
  }

  Future<Map<String, dynamic>> fetchConversationMessagesNew(
      String conversationId) async {
    try {
      final dio = Dio();

      final url = 'http://10.26.1.52:8200/api/v1/brd/$conversationId/messages';

      final response = await dio.get(url);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to fetch messages: ${response.statusCode}',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': 'Dio error: ${e.message}',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Unexpected error: $e',
      };
    }
  }

  Stream<Map<String, dynamic>> startStreaming({
    required String input,
  }) async* {
    try {
      final AuthService _authService = AuthService();
      final savedAuthData = await _authService.getSavedAuthData();
      final tenantId =
          savedAuthData.data?.user?.tenantId ?? 't001'; // Default fallback
      final userId = savedAuthData.data?.user?.id ?? 'user_001';

      // Keep project_id static for now as requested
      final projectId = "proj_${1000000 + math.Random().nextInt(1000)}";
      String? modelKey = await SharedPreferencesHelper.getSelectedModelApiKey();
      final payload = {
        "tenant": tenantId,
        "project": projectId,
        "user_id": userId,
        "initial_input": input,
        "preferred_model": modelKey ?? 'cladue',
        "industry": "retail",
        "team_size": "small team (2-10)",
        "solution_type": "ecommerce platform",
        "location": "India",
      };

      final response = await _dio.post(
        _startStreamUrl,
        data: payload,
        options: Options(
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          responseType: ResponseType.stream,
        ),
      );

      final stream = response.data.stream;
      String buffer = '';
      String? currentEventType;

      await for (final chunk
          in stream.cast<List<int>>().transform(utf8.decoder)) {
        buffer += chunk.replaceAll('\r\n', '\n');

        while (buffer.contains('\n')) {
          final index = buffer.indexOf('\n');
          final line = buffer.substring(0, index).trim();
          buffer = buffer.substring(index + 1);

          if (line.isEmpty) continue;

          if (line.startsWith('event: ')) {
            currentEventType = line.substring(7).trim();
          } else if (line.startsWith('data: ')) {
            final dataStr = line.substring(6);
            try {
              final data = jsonDecode(dataStr);
              yield {
                'event': currentEventType,
                'data': data,
              };
              if (currentEventType == 'end') break;
            } catch (e) {
              yield {'event': 'error', 'message': 'Parsing error: $e'};
            }
          }
        }
      }
    } catch (e) {
      yield {'event': 'error', 'message': 'Streaming error: $e'};
    }
  }

  // Stream<Map<String, dynamic>> startStreaming({
  //   required String input,
  // }) async* {
  //   try {
  //     final AuthService _authService = AuthService();
  //     final savedAuthData = await _authService.getSavedAuthData();
  //     final tenantId =
  //         savedAuthData.data?.user?.tenantId ?? 't001'; // Default fallback
  //     final userId = savedAuthData.data?.user?.id ?? 'user_001';

  //     // Keep project_id static for now as requested
  //     final projectId = "proj_${1000000 + math.Random().nextInt(1000)}";
  //     String? modelKey = await SharedPreferencesHelper.getSelectedModelApiKey();

  //     final payload = {
  //       "tenant": tenantId,
  //       "project": projectId,
  //       "user_id": userId,
  //       "initial_input": input,
  //       "industry": "retail",
  //       "team_size": "small team (2-10)",
  //       "solution_type": "ecommerce platform",
  //       "location": "India",
  //       "preferred_model": modelKey ?? 'claude',
  //     };

  //     Logger.info('Starting streaming with payload: $payload');

  //     final response = await _dio.post(
  //       _startStreamUrl,
  //       data: payload,
  //       options: Options(
  //         headers: {
  //           'Content-Type': 'application/json',
  //           'Accept': 'text/event-stream',
  //           'Cache-Control': 'no-cache',
  //           'Connection': 'keep-alive',
  //         },
  //         responseType: ResponseType.stream,
  //         receiveTimeout: Duration(minutes: 10), // Increase timeout further
  //         sendTimeout: Duration(minutes: 2),
  //         validateStatus: (status) =>
  //             status! < 500, // Accept all status codes under 500
  //       ),
  //     );

  //     Logger.info('Stream response status: ${response.statusCode}');

  //     if (response.statusCode == 200) {
  //       final stream = response.data.stream;
  //       String buffer = '';
  //       String? currentEventType;

  //       await for (final chunk
  //           in stream.cast<List<int>>().transform(utf8.decoder)) {
  //         buffer += chunk;

  //         // Process complete lines
  //         while (buffer.contains('\n')) {
  //           final lineEnd = buffer.indexOf('\n');
  //           final line = buffer.substring(0, lineEnd).trim();
  //           buffer = buffer.substring(lineEnd + 1);

  //           if (line.isEmpty) continue;

  //           Logger.info('Received SSE line: $line');

  //           // Handle event lines first
  //           if (line.startsWith('event: ')) {
  //             currentEventType = line.substring(7).trim();
  //             Logger.info('Event type: $currentEventType');
  //             continue;
  //           }

  //           // Parse Server-Sent Events format - handle data lines
  //           if (line.startsWith('data: ')) {
  //             final dataStr = line.substring(6);
  //             try {
  //               final data = jsonDecode(dataStr);
  //               Logger.info('Parsed SSE data: $data');

  //               // Use the event type from the event line, or fall back to data type
  //               final eventType = currentEventType ?? data['type'] ?? 'data';

  //               yield {
  //                 'event': eventType,
  //                 'data': data,
  //               };

  //               // Check for completion events
  //               if (eventType == 'complete' ||
  //                   eventType == 'end' ||
  //                   eventType == 'done' ||
  //                   eventType == 'result') {
  //                 Logger.info('Stream completed with event: $eventType');
  //                 break;
  //               }

  //               // Reset current event type after processing
  //               currentEventType = null;
  //             } catch (e) {
  //               Logger.error('Error parsing SSE data: $e');
  //               Logger.error('Raw data string: $dataStr');
  //               yield {'event': 'error', 'message': 'Parsing error: $e'};
  //               // Reset current event type on error
  //               currentEventType = null;
  //             }
  //           }
  //         }
  //       }
  //     } else {
  //       Logger.error(
  //           'Stream request failed with status: ${response.statusCode}');
  //       yield {
  //         'event': 'error',
  //         'message': 'HTTP ${response.statusCode}: ${response.statusMessage}'
  //       };
  //     }
  //   } catch (e) {
  //     Logger.error('Streaming error: $e');
  //     yield {'event': 'error', 'message': 'Streaming error: $e'};
  //   }
  // }

  Stream<Map<String, dynamic>> continueStreaming({
    required String conversationId,
    required String input,
  }) async* {
    try {
      String? modelKey = await SharedPreferencesHelper.getSelectedModelApiKey();
      final payload = {
        "conversation_id": conversationId,
        "user_input": input,
        "preferred_model": modelKey ?? 'cladue',
      };

      final response = await _dio.post(
        _continueStreamUrl,
        data: payload,
        options: Options(
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          responseType: ResponseType.stream,
        ),
      );

      final stream = response.data.stream;
      String buffer = '';
      String? currentEventType;

      await for (final chunk
          in stream.cast<List<int>>().transform(utf8.decoder)) {
        buffer += chunk.replaceAll('\r\n', '\n');

        while (buffer.contains('\n')) {
          final index = buffer.indexOf('\n');
          final line = buffer.substring(0, index).trim();
          buffer = buffer.substring(index + 1);

          if (line.isEmpty) continue;

          if (line.startsWith('event: ')) {
            currentEventType = line.substring(7).trim();
          } else if (line.startsWith('data: ')) {
            final dataStr = line.substring(6);
            try {
              final data = jsonDecode(dataStr);
              yield {
                'event': currentEventType,
                'data': data,
              };
              if (currentEventType == 'end') break;
            } catch (e) {
              yield {'event': 'error', 'message': 'Parsing error: $e'};
            }
          }
        }
      }
    } catch (e) {
      yield {'event': 'error', 'message': 'Streaming error: $e'};
    }
  }
}
