import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class FlexMapper {
  FlexMapper._();

  static int totalRowFlex = 4;

  static void setTotalRowFlex(int value) {
    if (value > 0) {
      totalRowFlex = value;
    }
  }

  /// Call this in your root widget or layout to set the flex value based on screen/platform
  static void setResponsiveFlex(BuildContext context) {
    // Use MediaQuery for width-based check (responsive)
    final screenWidth = MediaQuery.of(context).size.width;

    // Set totalRowFlex for mobile
    if (!kIsWeb && screenWidth < 767) {
      totalRowFlex = 1;
    } else {
      totalRowFlex = 4; // Web or larger screens
    }
  }

  static int getFlexValueForControl(String uiControlType) {
    if (_smallControls.contains(uiControlType)) return 1;
    if (_largeControls.contains(uiControlType)) return 4;
    return 2; // Default medium
  }

  static int getMaxItemsPerRow(String uiControlType) {
    final flex = getFlexValueForControl(uiControlType);
    return (totalRowFlex / flex).floor();
  }

  static const List<String> _smallControls = [
    'oj-checkbox',
    'oj-switch',
    'oj-radio',
    'oj-button',
    'oj-icon',
    'Dropdown',
  ];

  static const List<String> _largeControls = [
    'oj-text-area',
    'oj-rich-text',
    'oj-file-picker',
    'oj-image-picker',
    'oj-map',
    'oj-chart',
    'oj-table',
    'MultiLine',
  ];
}
