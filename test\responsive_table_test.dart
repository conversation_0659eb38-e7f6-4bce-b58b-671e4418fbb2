import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:nsl/screens/web/static_flow/extract_details_middle_static.dart';

void main() {
  group('Responsive Table Tests', () {
    testWidgets('Table columns should adjust to different screen sizes', (WidgetTester tester) async {
      // Test different screen sizes
      final List<Size> testSizes = [
        const Size(1920, 1080), // Desktop
        const Size(1366, 768),  // Laptop
        const Size(1024, 768),  // Tablet
        const Size(768, 1024),  // Small tablet
      ];

      for (final size in testSizes) {
        await tester.binding.setSurfaceSize(size);
        
        // Create a test widget with the ExtractDetailsMiddleStatic
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: SizedBox(
                width: size.width,
                height: size.height,
                child: const ExtractDetailsMiddleStatic(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify that the widget renders without overflow
        expect(tester.takeException(), isNull);
        
        // Check that table containers are present
        expect(find.byType(Container), findsWidgets);
        expect(find.byType(Row), findsWidgets);
        
        print('Screen size ${size.width}x${size.height}: Test passed');
      }
    });

    testWidgets('Table should have horizontal scroll when content exceeds width', (WidgetTester tester) async {
      // Test with a very narrow screen
      await tester.binding.setSurfaceSize(const Size(400, 800));
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: const SizedBox(
              width: 400,
              height: 800,
              child: ExtractDetailsMiddleStatic(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that SingleChildScrollView with horizontal scrolling is present
      final horizontalScrollViews = find.byWidgetPredicate(
        (widget) => widget is SingleChildScrollView && 
                   widget.scrollDirection == Axis.horizontal,
      );
      
      expect(horizontalScrollViews, findsWidgets);
      print('Horizontal scroll test: Passed');
    });

    test('Column width calculation should be responsive', () {
      // This test would require access to the _getColumnWidths method
      // Since it's private, we'll test the concept here
      
      final screenWidths = [1920.0, 1366.0, 1024.0, 768.0];
      
      for (final screenWidth in screenWidths) {
        final availableWidth = screenWidth - 120 - 48; // Actions column + padding
        final ratios = [0.2, 0.2, 0.15, 0.15, 0.3]; // Object Details ratios
        
        final calculatedWidths = ratios.map((ratio) {
          final calculatedWidth = availableWidth * ratio;
          return calculatedWidth < 100 ? 100.0 : calculatedWidth;
        }).toList();
        
        // Verify that all widths are reasonable
        for (final width in calculatedWidths) {
          expect(width, greaterThanOrEqualTo(100.0));
          expect(width, lessThanOrEqualTo(availableWidth));
        }
        
        print('Screen width $screenWidth: Column widths calculated correctly');
      }
    });
  });
}
